import { Dayjs } from 'dayjs'
import { IPageRequest } from '../../basic'

/* 签到列表*/
export class IMeetingSignInFilter extends IPageRequest {
  /* 会议表主键*/
  miceInfoId?: number
  /* 签到状态 0否1是 */
  isCheckIn?: boolean
  /* 会议ID */
  miceId?: string
}

/* 已签到会议人 */
export interface ISignInPersonFilter {
  /* 会议code */
  miceCode: string
}

/* 已签到会议人 */
export interface ISignInPerson {
  /* id */
  id?: number
  /* 会议code */
  miceCode: string
  /* 姓名 */
  nickName: string
  /* 电话号码 */
  phoneNo: string
  /* 身份证 */
  idCard: string
}

/* 签到规则 */
export class IMeetingSignInRules {
  id?: number
  // 会议表主键
  miceInfoId?: number
  // 是否开启签到
  isOpen?: boolean | number
  // 签到开始日期
  checkInStartDate?: string
  // 签到截止日期
  checkInEndDate?: string
  // 有效日期
  validDate?: [Dayjs, Dayjs]
  // 签到方式
  checkInRuleMethod?: number[] | string[]
  // 是否打开小程序自动签到
  isOpenCheckIn?: boolean | number
  // 签到二维码
  checkInCodeUrl?: string
  // 签到负责人姓称
  checkInHandleNickName?: string
  // 签到负责人工号
  checkInHandleUsername?: string
  // 是否开启签到通知 0否1是
  isCheckInNotice?: boolean | number
  // 签到范围
  checkInRange?: number
  checkList?: {
    id?: number
    // 签到地点名称
    place?: string
    // 签到地点经度
    longitude?: number
    // 签到地点纬度
    latitude?: number
  }[]
  // 签到二维码图片id
  checkInCodeId?: number
  // 二维码背景图片
  checkInCodeBgUrl?: string
  // 二维码图片类型
  checkInCodeType?: string | number
}

export class IMeetingSignInDetails {
  id?: number
  /* 应签到人工号*/
  username?: string
  // 应签到人姓名
  nickName?: string
  // 手机号
  phoneNo?: string
  // 身份证号
  idCard?: string
  // 性别
  sex?: string
  // 签到状态 0否1是
  isCheckIn?: number
  // 签到时间
  checkInTime?: string
  // 签到方式 0手动 1被动
  checkInMethod?: number
  // 签到人员工号
  checkInUsername?: number
  // 签到人员姓名
  checkInNickName?: string
  // 是否补签到
  isAdditionalCheckIn?: boolean
  // 补签到审批状态
  approveState?: number
  // 补签到审批人
  approvePerson?: string
  // 补签到审批时间
  approveTime?: string
  // 已签到人数
  checkInCount?: string
  // 未签到人数
  noCheckInCount?: string
  // 审批code
  approveCode?: string
}
