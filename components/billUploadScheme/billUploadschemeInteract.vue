<script setup lang="ts">
// 方案互动
import { message, Modal } from 'ant-design-vue';
import { onMounted, onBeforeUnmount, ref, reactive, computed, nextTick, inject, Ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { debounce } from 'lodash';
import {
  saveDataBy,
  getDataBy,
  delData,
  errorModal,
  resolveParam,
  routerParam,
  numComputedArrMethod,
  meetingProcessOrchestration,
  formatNumberThousands,
} from '@haierbusiness-front/utils';
import { schemeApi, miceBidManOrderListApi, meetingSignInApi, miceMerchantBillApi } from '@haierbusiness-front/apis';
import {
  IBillAssociatedType,
  IAttachmentFileType,
  MiceSchemeDetailsType,
  IBillDetail,
  IBillDetailStay,
  IBillDetailCatering,
  IBillDetailPlace,
  IBillDetailVehicle,
  IBillDetailAttendant,
  IBillDetailActivity,
  IBillDetailOther,
  IBillDetailAdditionalItem,
  IBillDetailAttachmentStatement,
  IBillDetailInsurance,
  IBillDetailPresent,
  IBillDetailMaterial,
  IBillDetailTraffic,
  IBillDetailAttachmentInvoice,
  IBillDetailServiceFee,
  IBillDetailAttachmentContract,
  IBillDetailAttachmentOther,
  IBillDetailBalance,
  miceSchemeSubmitRequest,
  ISchemeServiceFee,
  IBillUploadRequest,
  IBillUploadStay,
  IBillUploadCatering,
  IBillUploadPlace,
  IBillUploadVehicle,
  IBillUploadAttendant,
  IBillUploadActivity,
  IBillUploadInsurance,
  IBillUploadPresent,
  IBillUploadMaterial,
  IBillUploadTraffic,
  IBillUploadOther,
  IBillUploadServiceFee,
  IBillUploadAdditionalItem,
  IBillUploadAttachmentContract,
  IBillUploadAttachmentInvoice,
  IBillUploadAttachmentOther,
  IBillUploadBalance,
  ProcessOrchestrationServiceTypeEnum,
  MiceSchemeTypeConstant,
  MerchantType,
  IPriceQuantityAndTotalPriceType,
  IOtherAttachmentItem,
} from '@haierbusiness-front/common-libs';
import billUploadschemeInfo from './schemeComponent/billUploadschemeInfo.vue';
import schemeHotel from './schemeComponent/billUploadschemeHotel.vue';
import schemePlan from './schemeComponent/billUploadschemePlan.vue';
import schemeMaterial from './schemeComponent/billUploadschemeMaterial.vue';
import schemePresents from './schemeComponent/billUploadschemePresents.vue';
import schemeOther from './schemeComponent/billUploadschemeOther.vue';
import schemeServiceFee from './schemeComponent/billUploadschemeServiceFee.vue';
import schemeFiles from './schemeComponent/billUploadschemeFiles.vue';
import schemeTotal from './schemeComponent/billUploadschemeTotal.vue';
import billUploadschemeSupplementEntry from './schemeComponent/billUploadschemeSupplementEntry.vue';
import billUploadschemeHotelContract from './schemeComponent/billUploadschemeHotelContract.vue';
import billUploadschemeInvoice from './schemeComponent/billUploadschemeInvoice.vue';
import billUploadschemeWaterBill from './schemeComponent/billUploadschemeWaterBill.vue';
import billUploadschemeAccommodationDetail from './schemeComponent/billUploadschemeAccommodationDetail.vue';
import billUploadschemeConferencePhotos from './schemeComponent/billUploadschemeConferencePhotos.vue';
import billUploadschemeOtherAttachments from './schemeComponent/billUploadschemeOtherAttachments.vue';
import billUploadschemeInsuranceAttachment from './schemeComponent/billUploadschemeInsuranceAttachment.vue';
import ExportExpenseConfirmation from './schemeComponent/ExportExpenseConfirmation.vue';
import RelatedBillDialog from './schemeComponent/RelatedBillDialog.vue';
import { storeToRefs } from 'pinia';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

// 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
type schemeKeys =
  | 'view'
  | 'notReported'
  | 'reported'
  | 'schemeView'
  | 'notBidding'
  | 'biddingView'
  | 'billUpload'
  | undefined;

type SchemeItemType =
  | 'stays'
  | 'places'
  | 'caterings'
  | 'vehicles'
  | 'attendants'
  | 'activities'
  | 'material'
  | 'presents'
  | 'others'
  | 'additionalItems'
  | 'serviceFee';

const { loginUser } = storeToRefs(applicationStore());
// 获取基础URL
const baseUrl = import.meta.env.VITE_BASE_URL || '';

const route = useRoute();
const router = useRouter();

const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');

const schemeContainerRef = ref<any>(null);
const schemePlanRef = ref<any>(null);
const schemeMaterialRef = ref<any>(null);
const schemePresentRef = ref<any>(null);
const schemeOtherRef = ref<any>(null);
const schemeFeeRef = ref<any>(null);
const schemeFileRef = ref<any>(null);
const supplementEntryRef = ref<any>(null);
const billAttachmentRef = ref<any>(null);
const hotelContractRef = ref<any>(null);
const invoiceRef = ref<any>(null);
const waterBillRef = ref<any>(null);
const accommodationDetailRef = ref<any>(null);
const conferencePhotosRef = ref<any>(null);
const otherAttachmentsRef = ref<any>(null);
const insuranceAttachmentRef = ref<any>(null);
const BillschemeTotalRef = ref<any>(null);
const ExportExpenseConfirmationRef = ref<any>(null); // 添加结算单组件引用

// 应用编码
const applicationCode = ref('haierbusiness-mice-merchant');
const autoSave = ref<number | null>(null); // 自动保存
const countdownTimer = ref<number | null>(null); //
const countdownTime = ref<number>(60);
const cacheLoading = ref<boolean>(false);
const spinLoading = ref<boolean>(false);
const schemeLoading = ref<boolean>(false);
const isSchemeCache = ref<boolean>(true);
const subLoading = ref(false); // 完成提报

const abandonReason = ref<string>('');
const schemeAbandonReason = ref<string>(''); // 驳回内容反显

// 判断是否显示驳回原因
const shouldShowAbandonReason = computed(() => {
  if (!schemeAbandonReason.value) return false;

  // 获取当前用户的企业代码（作为商户代码）
  const currentMerchantCode = loginUser.value?.enterpriseCode;
  if (!currentMerchantCode) return false;

  // 获取有权限查看驳回原因的用户列表
  const visibleUsers = billDetail.value?.finalReverseReasonVisibleUser || '';
  if (!visibleUsers) return false;

  // 检查当前用户的企业代码是否在权限列表中
  const visibleUserList = visibleUsers.split(',').map((code: string) => code.trim());
  return visibleUserList.includes(currentMerchantCode);
});

const demandInfo = ref();
const hotelList = ref<Array<any>>([]); // 酒店
const schemePlanObj = ref<miceSchemeSubmitRequest>({}); // 每日计划
const schemeMaterialObj = ref<miceSchemeSubmitRequest>({}); // 布展物料
const schemePresentArr = ref<Array<any>>([]); // 礼品
const schemeOtherArr = ref<Array<IBillDetailOther>>([]); // 其他
const schemeFeeObj = ref<miceSchemeSubmitRequest>({}); // 全单服务费
const schemeFileObj = ref<Array<any>>([]); // 附件

// 账单附件相关数据
const billHotelList = ref<Array<any>>([]); // 账单酒店列表
const attachmentContracts = ref<Array<any>>([]); // 一手合同附件数据
const invoiceList = ref<Array<IBillDetailAttachmentInvoice>>([]); // 发票列表
const waterBillList = ref<Array<IBillDetailAttachmentStatement>>([]); // 水单列表
const accommodationDetailList = ref<Array<any>>([]); // 住宿详单列表
const conferencePhotoList = ref<Array<any>>([]); // 会议现场照片列表
const otherAttachmentList = ref<Array<any>>([]); // 其他附件列表
const additionalItems = ref<Array<IBillDetailAdditionalItem>>([]); // 补充条目列表
const insuranceAttachmentList = ref<Array<any>>([]); // 保单附件列表

// 关联账单弹框相关
const relatedBillVisible = ref(false);
const currentBillType = ref<'invoice' | 'waterBill'>('invoice');
const currentBillData = ref<any>(null);

const planPrice = ref<number>(0); // 每日计划 - 金额
const planEachPriceList = ref<Array<any>>([]); // 每日计划 - 金额
const materialPrice = ref<number>(0); // 布展物料 - 金额
const presentPrice = ref<number>(0); // 礼品 - 金额
const otherPrice = ref<number>(0); // 其他 - 金额
const totalPrice = ref<number>(0); // 全单服务费方案 - 总金额

const miceId = ref<number | null>(null);
const miceSchemeId = ref<number | null>(null);
const schemeType = ref<schemeKeys>(); // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
const hotelLockId = ref<string>('');
const miceSchemeDemandHotelLockId = ref<string>('');

const merchantId = ref<number | null>(null); // 服务商Id
const merchantType = ref<number | null>(null); // 服务商类型

const schemeDetail = ref<MiceSchemeDetailsType>({} as MiceSchemeDetailsType); // 方案详情（右侧）
const billDetail = ref<IBillDetail>({} as IBillDetail); // 账单详情

const schemeTotalInfo = ref<any>({}); // 合计详情

const processNode = ref<string>(''); // 流程节点

const showBindingScheme = ref<boolean>(true); // 展示标的方案
const showFee = ref<boolean>(false); // 全单服务费配置
const fullServiceRangeRateLimit = ref<number>(0); // 全单服务费
const fullServiceRemark = ref<string>(''); // 全单服务费
const serviceFeeSets = ref<Array<number> | undefined>([]); // 全单服务费配置项
const isCateringStandardControl = ref<string>(''); // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
const totalAmount = ref(); //总计金额
// 账单上传相关临时ID
const invoiceTempId = ref<string | null>(null); // 临时id, 用于关联发票表
const statementTempId = ref<string | null>(null); // 临时id, 用于关联水单表

const isShowDel = ref<boolean>(true); // 展示删除按钮

const pdMainId = ref<number>();
const pdVerId = ref<number | null>(null);

const leftPlanTotalPrice = ref();

const allRelatedBillIds = computed(() => {
  return getAllRelatedBillIds();
});

// 缓存查询
const getDetaileFromCache = async (): Promise<IBillDetail | undefined> => {
  if (miceId.value) {
    const cacheKey =
      'haierbusiness-mice-merchant_' +
      loginUser.value?.username +
      '_billUploadKey' +
      miceId.value +
      '_merchantId' +
      merchantId.value;

    const resCacheStr = await getDataBy({
      applicationCode: applicationCode.value,
      cacheKey: cacheKey, // 方案互动
    });

    if (resCacheStr) {
      billDetail.value = JSON.parse(resCacheStr) as IBillDetail;
      // 恢复各模块数据
      await restoreBillAttachmentData();
      return billDetail.value;
    }
  }
  return undefined;
};

const getSchemeDetails = async () => {
  // 方案详情
  schemeLoading.value = true;

  if (!miceId.value) {
    message.error('查询失败！');
    return;
  }

  // 服务商端 - 方案详情
  const res = await schemeApi.schemePlatDetails({
    miceId: miceId.value.toString(),
    schemeTypes: MiceSchemeTypeConstant.SCHEME_TYPE_EXECUTION.code.toString(), // 执行方案
  });

  if (res && res.length > 0) {
    // 流程详情
    const resData = Array.isArray(res) ? res[0] : res;
    leftPlanTotalPrice.value = resData?.schemeTotalPrice;

    await getProcessDetails(
      resData?.pdMainId?.toString(),
      resData?.pdVerId?.toString(),
      resData?.pdmMerchantPoolId?.toString(),
    );

    schemeDetail.value = resData || {};

    processNode.value = resData?.processNode || '';

    schemeLoading.value = false;

    // 账单上传模式下，处理酒店数据
    if (resData?.hotels) {
      // 初始化时先显示所有酒店，后续会根据住宿数据进行筛选
      billHotelList.value = resData.hotels.map((hotel: any) => ({
        id: hotel.id?.toString() || hotel.miceDemandPushHotelId?.toString() || Date.now().toString(),
        hotelName: hotel.hotelName || '未知酒店',
        contractFiles: [],
      }));

      // 如果已经有住宿数据，立即进行筛选
      nextTick(() => {
        updateBillHotelListBasedOnStays();
      });
    }
  }

  schemeLoading.value = false;

  // 获取签到数据
  if (schemeDetail.value?.mainCode) {
    await fetchSignInPersonList(schemeDetail.value.mainCode);
  }
};

const getBillDetails = async () => {
  debugger
  if (!miceId.value || !merchantId.value) {
    message.error('获取账单详情失败!');
    return;
  }

  const detailData = await miceMerchantBillApi.billDetails({ miceId: miceId.value!, merchantId: merchantId.value });
  if (detailData && detailData.length > 0) {
    billDetail.value = detailData[0];
    // 恢复账单附件数据
    await restoreBillAttachmentData();

    // 🔧 重建有效的关联关系
    rebuildInvoiceRelationshipsFromCache();
    rebuildWaterBillRelationshipsFromCache();

    // 🔧 新增：先清理无效的关联ID（只清理不存在的发票/水单关联）
    // cleanInvalidRelationIds();

    // 🏨 根据缓存的住宿数据更新酒店列表
    nextTick(() => {
      updateBillHotelListBasedOnStays();
    });
  } else {
    billDetail.value = {} as IBillDetail;
    isSchemeCache.value = false;
  }
};

// 恢复账单附件数据
const restoreBillAttachmentData = async () => {
  // 驳回内容反显
  schemeAbandonReason.value = billDetail.value?.finalReverseReason || '';

  // 恢复账单附件数据
  if (billDetail.value) {
    // 处理发票数据，将 paths 转换为 attachmentFiles
    invoiceList.value =
      billDetail.value.attachmentInvoices?.map((invoice) => {
        const processedInvoice = {
          ...invoice,
          // 确保必要字段有默认值
          relatedBill: '查看关联>>',
          relatedAmountTotalCny: invoice.relatedAmountTotalCny || 0,
        };

        // 如果有 paths 但没有 attachmentFiles，则转换 paths 为 attachmentFiles
        if (
          invoice.paths &&
          invoice.paths.length > 0 &&
          (!invoice.attachmentFiles || invoice.attachmentFiles.length === 0)
        ) {
          processedInvoice.attachmentFiles = invoice.paths.map((path: string, index: number) => {
            // 处理路径，确保正确的 URL 格式
            let processedPath = path;

            // 如果路径已经包含完整 URL，提取相对路径
            if (path.includes(baseUrl)) {
              processedPath = path.replace(baseUrl, '');
            }

            // 确保路径以 / 开头
            if (!processedPath.startsWith('/')) {
              processedPath = '/' + processedPath;
            }

            return {
              uid: `${invoice.tempId}_${index}`,
              name: path.split('/').pop() || `附件${index + 1}`,
              status: 'done' as const,
              url: baseUrl + processedPath,
              filePath: processedPath,
              fileName: path.split('/').pop() || `附件${index + 1}`,
            };
          });
        }

        return processedInvoice;
      }) || [];
    // 处理水单数据，将 paths 转换为 attachmentFiles
    waterBillList.value =
      billDetail.value.attachmentStatements?.map((waterBill) => {
        const processedWaterBill = {
          ...waterBill,
          // 确保必要字段有默认值
          relatedBill: waterBill.relatedBill || '关联>>',
          relatedAmountTotalCny: waterBill.relatedAmountTotalCny || 0,
        };

        // 如果有 paths 但没有 attachmentFiles，则转换 paths 为 attachmentFiles
        if (
          waterBill.paths &&
          waterBill.paths.length > 0 &&
          (!waterBill.attachmentFiles || waterBill.attachmentFiles.length === 0)
        ) {
          processedWaterBill.attachmentFiles = waterBill.paths.map((path: string, index: number) => {
            // 处理路径，确保正确的 URL 格式
            let processedPath = path;

            // 如果路径已经包含完整 URL，提取相对路径
            if (path.includes(baseUrl)) {
              processedPath = path.replace(baseUrl, '');
            }

            // 确保路径以 / 开头
            if (!processedPath.startsWith('/')) {
              processedPath = '/' + processedPath;
            }

            return {
              uid: `${waterBill.tempId || Date.now()}_${index}`,
              name: path.split('/').pop() || `附件${index + 1}`,
              status: 'done' as const,
              url: baseUrl + processedPath,
              filePath: processedPath,
              fileName: path.split('/').pop() || `附件${index + 1}`,
            };
          });
        }

        return processedWaterBill;
      }) || [];

    accommodationDetailList.value = billDetail.value.attachmentStayChecks || [];
    conferencePhotoList.value = billDetail.value.attachmentPhotos || [];
    // 🔧 如果缓存中的照片数据为空数组，需要在下一个tick中通知子组件已初始化，避免自动添加空数据
    if (conferencePhotoList.value.length === 0) {
      nextTick(() => {
        // 通过ref直接设置子组件的初始化状态（如果子组件暴露了相关方法）
        if (conferencePhotosRef.value && conferencePhotosRef.value.setInitialized) {
          conferencePhotosRef.value.setInitialized(true);
        }
      });
    }

    // 处理其他附件反显，按照组件要求的格式处理（临时在这处理，后期放到组件里自行处理）
    const otherAttachments:IOtherAttachmentItem[] = [] as IOtherAttachmentItem[];
    billDetail.value.attachmentOthers?.map((item) => {
      const files = item.paths.map((path: string, pathIndex: number) => {
        let processedPath = path;
        // 如果路径已经包含完整 URL，直接使用
        if (path.startsWith('http')) {
          processedPath = path;
        } else {
          // 如果是相对路径，拼接 baseUrl
          processedPath = path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
        }

        // 生成文件名
        const fileName = path.split('/').pop() || `其他附件${pathIndex + 1}`;

        return {
          uid: `${Math.floor(Math.random() * 90000000) + 10000000}`,
          name: fileName,
          status: 'done',
          url: processedPath,
          filePath: processedPath,
        };
      });

      otherAttachments.push({
        ...item,
        files: files,
      });
    });

    otherAttachmentList.value = otherAttachments || [];
    attachmentContracts.value = billDetail.value.attachmentContracts || [];

    // 为组件需要的变量赋值
    restoreReactiveVariables();

    // 恢复结算单导入状态
    if (billDetail.value.balances && ExportExpenseConfirmationRef.value) {
      nextTick(() => {
        ExportExpenseConfirmationRef.value.initFromCache(billDetail.value);
      });
    }
  }
};

// 酒店
const hotelsEmit = (hotelArr: Array<any>) => {
  hotelList.value = [...hotelArr];
};
// 根据住宿数据筛选一手合同中显示的酒店
const updateBillHotelListBasedOnStays = () => {
  // 只在账单上传模式下执行筛选
  if (schemeType.value !== 'billUpload') {
    return;
  }

  // 确保有必要的数据
  if (!schemeDetail.value?.hotels) {
    return;
  }

  // 如果没有住宿数据，显示所有酒店（避免用户困惑）
  if (!schemePlanObj.value?.stays || schemePlanObj.value.stays.length === 0) {
    billHotelList.value = schemeDetail.value.hotels.map((hotel: any) => ({
      id: hotel.id?.toString() || hotel.miceDemandPushHotelId?.toString() || Date.now().toString(),
      hotelName: hotel.hotelName || '未知酒店',
      contractFiles:
        billHotelList.value.find(
          (existing: any) => existing.id === (hotel.id?.toString() || hotel.miceDemandPushHotelId?.toString()),
        )?.contractFiles || [], // 保留已上传的合同文件
    }));
    return;
  }

  // 从住宿数据中提取使用的酒店ID
  const usedHotelIds = new Set();
  schemePlanObj.value.stays.forEach((stay: any) => {
    if (stay.miceDemandHotelId) {
      usedHotelIds.add(stay.miceDemandHotelId);
    }
    if (stay.miceSchemeHotelId) {
      usedHotelIds.add(stay.miceSchemeHotelId);
    }
    if (stay.miceDemandPushHotelId) {
      usedHotelIds.add(stay.miceDemandPushHotelId);
    }
  });

  // 筛选出实际使用的酒店
  const usedHotels = schemeDetail.value.hotels.filter(
    (hotel: any) =>
      usedHotelIds.has(hotel.id) ||
      usedHotelIds.has(hotel.miceDemandPushHotelId) ||
      usedHotelIds.has(hotel.miceDemandHotelId),
  );

  // 更新账单酒店列表，保留已上传的合同文件
  billHotelList.value = usedHotels.map((hotel: any) => {
    const existingHotel = billHotelList.value.find(
      (existing: any) => existing.id === (hotel.id?.toString() || hotel.miceDemandPushHotelId?.toString()),
    );

    return {
      id: hotel.id?.toString() || hotel.miceDemandPushHotelId?.toString() || Date.now().toString(),
      hotelName: hotel.hotelName || '未知酒店',
      contractFiles: existingHotel?.contractFiles || [], // 保留已上传的合同文件
    };
  });
};

// 日程安排
const schemePlanEmit = (miceSchemeSubData: miceSchemeSubmitRequest) => {
  console.log('日程安排里有啥：', miceSchemeSubData);
  const preserveRelationIds = (existingData: any, newData: any) => {
    if (!existingData || !newData) return newData;

    // 保留住宿关联ID
    if (existingData.stays && newData.stays) {
      newData.stays.forEach((newStay: any, index: number) => {
        const existingStay = existingData.stays.find(
          (stay: any) =>
            (stay.id && newStay.id && stay.id === newStay.id) ||
            (stay.tempId && newStay.tempId && stay.tempId === newStay.tempId) ||
            (stay.miceDemandStayId && newStay.miceDemandStayId && stay.miceDemandStayId === newStay.miceDemandStayId) ||
            (stay.miceSchemeStayId && newStay.miceSchemeStayId && stay.miceSchemeStayId === newStay.miceSchemeStayId),
        );
        if (existingStay) {
          newStay.invoiceTempId = newStay.invoiceTempId || existingStay.invoiceTempId;
          newStay.statementTempId = newStay.statementTempId || existingStay.statementTempId;
        }
      });
    }

    // 保留会场关联ID
    if (existingData.places && newData.places) {
      newData.places.forEach((newPlace: any) => {
        const existingPlace = existingData.places.find(
          (place: any) =>
            (place.id && newPlace.id && place.id === newPlace.id) ||
            (place.tempId && newPlace.tempId && place.tempId === newPlace.tempId) ||
            (place.miceDemandPlaceId &&
              newPlace.miceDemandPlaceId &&
              place.miceDemandPlaceId === newPlace.miceDemandPlaceId) ||
            (place.miceSchemePlaceId &&
              newPlace.miceSchemePlaceId &&
              place.miceSchemePlaceId === newPlace.miceSchemePlaceId),
        );
        if (existingPlace) {
          newPlace.invoiceTempId = newPlace.invoiceTempId || existingPlace.invoiceTempId;
          newPlace.statementTempId = newPlace.statementTempId || existingPlace.statementTempId;
        }
      });
    }

    // 保留用餐关联ID
    if (existingData.caterings && newData.caterings) {
      newData.caterings.forEach((newCatering: any) => {
        const existingCatering = existingData.caterings.find(
          (catering: any) =>
            (catering.id && newCatering.id && catering.id === newCatering.id) ||
            (catering.tempId && newCatering.tempId && catering.tempId === newCatering.tempId) ||
            (catering.miceDemandCateringId &&
              newCatering.miceDemandCateringId &&
              catering.miceDemandCateringId === newCatering.miceDemandCateringId) ||
            (catering.miceSchemeCateringId &&
              newCatering.miceSchemeCateringId &&
              catering.miceSchemeCateringId === newCatering.miceSchemeCateringId),
        );
        if (existingCatering) {
          newCatering.invoiceTempId = newCatering.invoiceTempId || existingCatering.invoiceTempId;
          newCatering.statementTempId = newCatering.statementTempId || existingCatering.statementTempId;
        }
      });
    }

    // 保留用车关联ID
    if (existingData.vehicles && newData.vehicles) {
      newData.vehicles.forEach((newVehicle: any) => {
        const existingVehicle = existingData.vehicles.find(
          (vehicle: any) =>
            (vehicle.id && newVehicle.id && vehicle.id === newVehicle.id) ||
            (vehicle.tempId && newVehicle.tempId && vehicle.tempId === newVehicle.tempId) ||
            (vehicle.miceDemandVehicleId &&
              newVehicle.miceDemandVehicleId &&
              vehicle.miceDemandVehicleId === newVehicle.miceDemandVehicleId) ||
            (vehicle.miceSchemeVehicleId &&
              newVehicle.miceSchemeVehicleId &&
              vehicle.miceSchemeVehicleId === newVehicle.miceSchemeVehicleId),
        );
        if (existingVehicle) {
          newVehicle.invoiceTempId = newVehicle.invoiceTempId || existingVehicle.invoiceTempId;
          newVehicle.statementTempId = newVehicle.statementTempId || existingVehicle.statementTempId;
        }
      });
    }

    // 保留服务人员关联ID
    if (existingData.attendants && newData.attendants) {
      newData.attendants.forEach((newAttendant: any) => {
        const existingAttendant = existingData.attendants.find(
          (attendant: any) =>
            (attendant.id && newAttendant.id && attendant.id === newAttendant.id) ||
            (attendant.tempId && newAttendant.tempId && attendant.tempId === newAttendant.tempId) ||
            (attendant.miceDemandAttendantId &&
              newAttendant.miceDemandAttendantId &&
              attendant.miceDemandAttendantId === newAttendant.miceDemandAttendantId) ||
            (attendant.miceSchemeAttendantId &&
              newAttendant.miceSchemeAttendantId &&
              attendant.miceSchemeAttendantId === newAttendant.miceSchemeAttendantId),
        );
        if (existingAttendant) {
          newAttendant.invoiceTempId = newAttendant.invoiceTempId || existingAttendant.invoiceTempId;
          newAttendant.statementTempId = newAttendant.statementTempId || existingAttendant.statementTempId;
        }
      });
    }

    // 保留活动关联ID
    if (existingData.activities && newData.activities) {
      newData.activities.forEach((newActivity: any) => {
        const existingActivity = existingData.activities.find(
          (activity: any) =>
            (activity.id && newActivity.id && activity.id === newActivity.id) ||
            (activity.tempId && newActivity.tempId && activity.tempId === newActivity.tempId) ||
            (activity.miceDemandActivityId &&
              newActivity.miceDemandActivityId &&
              activity.miceDemandActivityId === newActivity.miceDemandActivityId) ||
            (activity.miceSchemeActivityId &&
              newActivity.miceSchemeActivityId &&
              activity.miceSchemeActivityId === newActivity.miceSchemeActivityId),
        );
        if (existingActivity) {
          newActivity.invoiceTempId = newActivity.invoiceTempId || existingActivity.invoiceTempId;
          newActivity.statementTempId = newActivity.statementTempId || existingActivity.statementTempId;
        }
      });
    }

    return newData;
  };

  // 应用关联ID保护
  const protectedData = preserveRelationIds(schemePlanObj.value, miceSchemeSubData);
  schemePlanObj.value = { ...protectedData };

  // 一次性调用更新
  schemePriceOrQuantityChange('stays');
  schemePriceOrQuantityChange('caterings');
  schemePriceOrQuantityChange('vehicles');
  schemePriceOrQuantityChange('attendants');
  schemePriceOrQuantityChange('activities');
  schemePriceOrQuantityChange('places');


  // 根据住宿数据更新一手合同中的酒店列表
  nextTick(() => {
    updateBillHotelListBasedOnStays();
  });
};
// 布展物料
const schemeMaterialEmit = (materialObj: miceSchemeSubmitRequest) => {
  console.log('布展物料里有啥：', materialObj);
  // 🔧 保留现有的关联ID，避免被新数据覆盖（只处理主对象）
  const existingMaterial = schemeMaterialObj.value?.material;
  const newMaterial = materialObj.material;

  if (existingMaterial && newMaterial) {
    // 只保留主对象的关联ID
    newMaterial.invoiceTempId = newMaterial.invoiceTempId || existingMaterial.invoiceTempId;
    newMaterial.statementTempId = newMaterial.statementTempId || existingMaterial.statementTempId;

    // 🔧 确保明细中不包含关联字段
    if (newMaterial.materialDetails) {
      newMaterial.materialDetails = newMaterial.materialDetails.map((detail: any) => {
        const { invoiceTempId, statementTempId, ...cleanDetail } = detail;
        return cleanDetail;
      });
    }
  }
  schemeMaterialObj.value = { material: newMaterial };
  schemePriceOrQuantityChange('material');
};
// 礼品
const schemePresentEmit = (presentArr: Array<any>) => {
  // 🔧 修复：保留现有的关联ID，避免被新数据覆盖
  const preservePresentRelationIds = (existingPresents: any[], newPresents: any[]) => {
    if (!existingPresents || !newPresents) return newPresents;

    return newPresents.map((newPresent: any) => {
      const existingPresent = existingPresents.find(
        (present: any) =>
          (present.id && newPresent.id && present.id === newPresent.id) ||
          (present.tempId && newPresent.tempId && present.tempId === newPresent.tempId),
      );

      if (existingPresent) {
        // 保留主记录的关联ID
        newPresent.invoiceTempId = newPresent.invoiceTempId || existingPresent.invoiceTempId;
        newPresent.statementTempId = newPresent.statementTempId || existingPresent.statementTempId;

        // 保留明细的关联ID
        if (existingPresent.presentDetails && newPresent.presentDetails) {
          newPresent.presentDetails = newPresent.presentDetails.map((newDetail: any) => {
            const existingDetail = existingPresent.presentDetails.find(
              (detail: any) =>
                (detail.id && newDetail.id && detail.id === newDetail.id) ||
                (detail.tempId && newDetail.tempId && detail.tempId === newDetail.tempId),
            );

            if (existingDetail) {
              newDetail.invoiceTempId = newDetail.invoiceTempId || existingDetail.invoiceTempId;
              newDetail.statementTempId = newDetail.statementTempId || existingDetail.statementTempId;
            }

            return newDetail;
          });
        }
      }

      return newPresent;
    });
  };

  const protectedPresents = preservePresentRelationIds(schemePresentArr.value, presentArr);
  schemePresentArr.value = [...protectedPresents];
};

// 其他
const schemeOtherEmit = (otherArr: Array<IBillDetailOther>) => {
  console.log('其他里有啥：', otherArr);
  schemeOtherArr.value = [...otherArr];
  schemePriceOrQuantityChange('others');
};
// 全单服务费
const schemeFeeEmit = (feeObj: miceSchemeSubmitRequest) => {
  console.log('服务费里有啥：', feeObj);
  // 🔧 修复：保留现有的关联ID，避免被新数据覆盖
  const preserveServiceFeeRelationIds = (existingFee: any, newFee: any) => {
    if (!existingFee || !newFee) return newFee;

    // 保留服务费的关联ID
    if (existingFee.serviceFee && newFee.serviceFee) {
      newFee.serviceFee.invoiceTempId = newFee.serviceFee.invoiceTempId || existingFee.serviceFee.invoiceTempId;
      newFee.serviceFee.statementTempId = newFee.serviceFee.statementTempId || existingFee.serviceFee.statementTempId;
    }

    return newFee;
  };

  const protectedFeeObj = preserveServiceFeeRelationIds(schemeFeeObj.value, feeObj);
  schemeFeeObj.value = { ...protectedFeeObj };
  schemeTotalInfo.value = { ...schemeTotalInfo.value, ...protectedFeeObj };
  schemePriceOrQuantityChange('serviceFee');
};

// 价格或数量修改后，如果已经关联了账单或者水单需要重新修改对应的金额
const schemePriceOrQuantityChange = (key: SchemeItemType) => {
  const billData: IBillDetail = schemePlanObj.value as unknown as IBillDetail;
  const otherData: IBillDetailOther = schemeOtherArr.value as unknown as IBillDetailOther;
  const additionalItemsData: IBillDetailAdditionalItem = additionalItems.value as unknown as IBillDetailAdditionalItem;
  const materialData: IBillUploadRequest = schemeMaterialObj.value as unknown as IBillUploadRequest;
  const serviceFeesData: IBillDetail = schemeFeeObj.value as unknown as IBillDetail;
  // 根据key获取确定是哪个数据源
  switch (key) {
    case 'stays':
    case 'caterings':
    case 'vehicles':
    case 'attendants':
    case 'activities':
    case 'places':
      // 分别获取invoiceTempId和statementTempId不为空的对象
      const updateInvoiceList = billData[key]?.filter((item: any) => item.invoiceTempId);
      const updateStatementList = billData[key]?.filter((item: any) => item.statementTempId);
      //分别判断各自的数组是否有值，有值则修改对应的金额
      if (updateInvoiceList && updateInvoiceList?.length > 0) {
        updateInvoiceList.forEach((item) => {
          const { billUnitPrice, billQuantity, billTotalPrice } = getPriceQuantityAndTotalPrice(item, key);
          // 去找对应的发票下关联的账单修改金额
          invoiceList.value.forEach((invoice: any) => {
            if (invoice.tempId == item.invoiceTempId) {
              let relatedAmountTotalCny = 0;
              invoice.relatedBills.forEach((bill: any) => {
                // 当发票中关联数据的id等于'key_'+ item[getSchemeIdField(key)]时，修改发票中关联的账单金额
                if (bill.id == `${key}_${item[getSchemeIdField(key)]}`) {
                  bill.billUnitPrice = billUnitPrice;
                  bill.billQuantity = billQuantity;
                  bill.billPrice = billTotalPrice;
                }
                relatedAmountTotalCny += bill.billPrice;
              });
              invoice.relatedAmountTotalCny = relatedAmountTotalCny;
            }
          });
        });
      }
      if (updateStatementList && updateStatementList?.length > 0) {
        updateStatementList.forEach((item) => {
          const { billUnitPrice, billQuantity, billTotalPrice } = getPriceQuantityAndTotalPrice(item, key);
          // 去找对应的水单下关联的账单修改金额
          waterBillList.value.forEach((statement: any) => {
            if (statement.tempId == item.statementTempId) {
              let relatedAmountTotalCny = 0;
              statement.relatedBills.forEach((bill: any) => {
                if (bill.id == `${key}_${item[getSchemeIdField(key)]}`) {
                  bill.billUnitPrice = billUnitPrice;
                  bill.billQuantity = billQuantity;
                  bill.billPrice = billTotalPrice;
                }
                relatedAmountTotalCny += bill.billPrice;
              });
              statement.relatedAmountTotalCny = relatedAmountTotalCny;
            }
          });
        });
      }
      break;
    case 'others':
      // 分别获取invoiceTempId和statementTempId不为空的对象
      const updateOtherInvoiceList = otherData[key]?.filter((item: any) => item.invoiceTempId);
      const updateOtherStatementList = otherData[key]?.filter((item: any) => item.statementTempId);
      //分别判断各自的数组是否有值，有值则修改对应的金额
      if (updateOtherInvoiceList && updateOtherInvoiceList?.length > 0) {
        updateOtherInvoiceList.forEach((item) => {
          const { billUnitPrice, billQuantity, billTotalPrice } = getPriceQuantityAndTotalPrice(item, key);
          // 去找对应的发票下关联的账单修改金额
          invoiceList.value.forEach((invoice: any) => {
            if (invoice.tempId == item.invoiceTempId) {
              let relatedAmountTotalCny = 0;
              invoice.relatedBills.forEach((bill: any) => {
                if (bill.id == `${key}_${item[getSchemeIdField(key)]}`) {
                  bill.billUnitPrice = billUnitPrice;
                  bill.billQuantity = billQuantity;
                  bill.billPrice = billTotalPrice;
                }
                relatedAmountTotalCny += bill.billPrice;
              });
              invoice.relatedAmountTotalCny = relatedAmountTotalCny;
            }
          });
        });
      }
      if (updateOtherStatementList && updateOtherStatementList?.length > 0) {
        updateOtherStatementList.forEach((item) => {
          const { billUnitPrice, billQuantity, billTotalPrice } = getPriceQuantityAndTotalPrice(item, key);
          // 去找对应的水单下关联的账单修改金额
          waterBillList.value.forEach((statement: any) => {
            if (statement.tempId == item.statementTempId) {
              let relatedAmountTotalCny = 0;
              statement.relatedBills.forEach((bill: any) => {
                if (bill.id == `${key}_${item[getSchemeIdField(key)]}`) {
                  bill.billUnitPrice = billUnitPrice;
                  bill.billQuantity = billQuantity;
                  bill.billPrice = billTotalPrice;
                }
                relatedAmountTotalCny += bill.billPrice;
              });
              statement.relatedAmountTotalCny = relatedAmountTotalCny;
            }
          });
        });
      }
      break;
    case 'additionalItems': 
      // 分别获取invoiceTempId和statementTempId不为空的对象
      const updateAdditionalItemsInvoiceList = additionalItemsData.additionalItems?.filter((item: any) => item.invoiceTempId);
      const updateAdditionalItemsStatementList = additionalItemsData.additionalItems?.filter((item: any) => item.statementTempId);
      //分别判断各自的数组是否有值，有值则修改对应的金额
      if (updateAdditionalItemsInvoiceList && updateAdditionalItemsInvoiceList?.length > 0) {
        updateAdditionalItemsInvoiceList.forEach((item) => {
          const { billUnitPrice, billQuantity, billTotalPrice } = getPriceQuantityAndTotalPrice(item, key);
          // 去找对应的发票下关联的账单修改金额
          invoiceList.value.forEach((invoice: any) => {
            if (invoice.tempId == item.invoiceTempId) {
              let relatedAmountTotalCny = 0;
              invoice.relatedBills.forEach((bill: any) => {
                if (bill.id == `${key}_${item[getSchemeIdField(key)]}`) {
                  bill.billUnitPrice = billUnitPrice;
                  bill.billQuantity = billQuantity;
                  bill.billPrice = billTotalPrice;
                }
                relatedAmountTotalCny += bill.billPrice;
              });
              invoice.relatedAmountTotalCny = relatedAmountTotalCny;
            }
          });
        });
      }
      if (updateAdditionalItemsStatementList && updateAdditionalItemsStatementList?.length > 0) {
        updateAdditionalItemsStatementList.forEach((item) => {
          const { billUnitPrice, billQuantity, billTotalPrice } = getPriceQuantityAndTotalPrice(item, key);
          // 去找对应的水单下关联的账单修改金额
          waterBillList.value.forEach((statement: any) => {
            if (statement.tempId == item.statementTempId) {
              let relatedAmountTotalCny = 0;
              statement.relatedBills.forEach((bill: any) => {
                if (bill.id == `${key}_${item[getSchemeIdField(key)]}`) {
                  bill.billUnitPrice = billUnitPrice;
                  bill.billQuantity = billQuantity;
                  bill.billPrice = billTotalPrice;
                }
                relatedAmountTotalCny += bill.billPrice;
              });
              statement.relatedAmountTotalCny = relatedAmountTotalCny;
            }
          });
        });
      }
      break;
    case 'material':
      // 直接判断对应字段是否有值
      if (materialData.material?.invoiceTempId) {
        // 去找对应的发票下关联的账单修改金额
        invoiceList.value.forEach((invoice: any) => {
          if (invoice.tempId == materialData.material?.invoiceTempId) {
            let relatedAmountTotalCny = 0;
            invoice.relatedBills.forEach((bill: any) => {
              if (bill.id == `${key}_${materialData.material!.miceSchemeMaterialId!}`) {
                bill.billUnitPrice = materialData.material?.billTotalPrice;
                bill.billQuantity = 1;
                bill.billPrice = materialData.material?.billTotalPrice;
              }
              relatedAmountTotalCny += bill.billPrice;
            });
            invoice.relatedAmountTotalCny = relatedAmountTotalCny;
          }
        });
      }
      if (materialData.material?.statementTempId) {
        // 去找对应的水单下关联的账单修改金额
        waterBillList.value.forEach((statement: any) => {
          if (statement.tempId == materialData.material?.statementTempId) {
            let relatedAmountTotalCny = 0;
            statement.relatedBills.forEach((bill: any) => {
              if (bill.id == `${key}_${materialData.material!.miceSchemeMaterialId!}`) {
                bill.billUnitPrice = materialData.material?.billTotalPrice;
                bill.billQuantity = 1;
                bill.billPrice = materialData.material?.billTotalPrice;
              }
              relatedAmountTotalCny += bill.billPrice;
            });
            statement.relatedAmountTotalCny = relatedAmountTotalCny;
          }
        });
      }
      break;
    case 'serviceFee':
      // 直接判断对应字段是否有值
      if (serviceFeesData.serviceFee?.invoiceTempId) {
        // 去找对应的发票下关联的账单修改金额
        invoiceList.value.forEach((invoice: any) => {
          if (invoice.tempId == serviceFeesData.serviceFee?.invoiceTempId) {
            let relatedAmountTotalCny = 0;
            invoice.relatedBills.forEach((bill: any) => {
              if (bill.id == `${key}_${serviceFeesData.serviceFee!.miceSchemeServiceFeeId!}`) { 
                bill.billUnitPrice = serviceFeesData.serviceFee!.billServiceFeeReal;
                bill.billQuantity = 1;
                bill.billPrice = serviceFeesData.serviceFee!.billServiceFeeReal;
              }
              relatedAmountTotalCny += bill.billPrice
            });
            invoice.relatedAmountTotalCny = relatedAmountTotalCny;
          }
        });
      }
      if (serviceFeesData.serviceFee?.statementTempId) {
        // 去找对应的水单下关联的账单修改金额
        waterBillList.value.forEach((statement: any) => {
          if (statement.tempId == serviceFeesData.serviceFee?.statementTempId) {
            let relatedAmountTotalCny = 0;
            statement.relatedBills.forEach((bill: any) => {
              if (bill.id == `${key}_${serviceFeesData.serviceFee!.miceSchemeServiceFeeId!}`) { 
                bill.billUnitPrice = serviceFeesData.serviceFee!.billServiceFeeReal;
                bill.billQuantity = 1;
                bill.billPrice = serviceFeesData.serviceFee!.billServiceFeeReal;
              }
              relatedAmountTotalCny += bill.billPrice
            });
            statement.relatedAmountTotalCny = relatedAmountTotalCny;
          }
        });
      }
      break;
    default:
      break;
  }
  
};

//统一封装获取竞价和账单的单价，数量和总价
const getPriceQuantityAndTotalPrice = (item: any, type: SchemeItemType): IPriceQuantityAndTotalPriceType => {
  switch (type) {
    case 'stays':
    case 'caterings':
    case 'vehicles':
    case 'attendants':
    case 'activities':
      return {
        schemeUnitPrice: item.schemeUnitPrice || 0,
        schemeQuantity: item.schemeRoomNum || item.schemePersonNum || item.schemeVehicleNum || 0,
        billUnitPrice: item.billUnitPrice || 0,
        billQuantity: item.billRoomNum || item.billPersonNum || item.billVehicleNum || 0,
        billTotalPrice: (item.billUnitPrice || 0) * (item.billRoomNum || item.billPersonNum || item.billVehicleNum || 0),
      };
    case 'places':
      return {
        schemeUnitPrice: Number(
          (item.schemeUnitPlacePrice || 0) +
            (item.hasLed ? (item.schemeUnitLedPrice || 0) * (item.schemeLedNum || 0) : 0) +
            (item.hasTea ? (item.teaEachTotalPrice || 0) * (item.schemePersonNum || 0) : 0),
        ),
        schemeQuantity: 1,
        billUnitPrice: Number(
          (item.billUnitPlacePrice || 0) +
            (item.hasLed ? (item.billUnitLedPrice || 0) * (item.billLedNum || 0) : 0) +
            (item.hasTea ? (item.billUnitTeaPrice || 0) * (item.billPersonNum || 0) : 0),
        ),
        billQuantity: 1,
        billTotalPrice: Number(
          (item.billUnitPlacePrice || 0) +
            (item.hasLed ? (item.billUnitLedPrice || 0) * (item.billLedNum || 0) : 0) +
            (item.hasTea ? (item.billUnitTeaPrice || 0) * (item.billPersonNum || 0) : 0),
        ),
      };
    case 'others':
      return {
        schemeUnitPrice: item.num ? Number((Number(item.schemeTotalPrice) / Number(item.num || 1)).toFixed(2)) : 0,
        schemeQuantity: item.num || 1,
        billUnitPrice: item.billNum ? Number((Number(item.billTotalPrice) / Number(item.billNum || 1)).toFixed(2)) : 0,
        billQuantity: item.billNum || 1,
        billTotalPrice: item.billTotalPrice || 0,
      };
    case 'serviceFee':
      return {
        schemeUnitPrice: item.schemeServiceFeeReal || 0,
        schemeQuantity: 1,
        billUnitPrice: item.billServiceFeeReal || 0,
        billQuantity: 1,
        billTotalPrice: item.billServiceFeeReal || 0,
      };
    default:
      return {} as IPriceQuantityAndTotalPriceType;
  }
};

//根据不同的key返回不同账单下的方案id字段名
const getSchemeIdField = (key: SchemeItemType): string => {
  switch (key) {
    case 'stays':
      return 'miceSchemeStayId';
    case 'places':
      return 'miceSchemePlaceId';
    case 'caterings':
      return 'miceSchemeCateringId';
    case 'vehicles':
      return 'miceSchemeVehicleId';
    case 'attendants':
      return 'miceSchemeAttendantId';
    case 'activities':
      return 'miceSchemeActivityId';
    case 'material':
      return 'miceSchemeMaterialId';
    case 'presents':
      return 'miceSchemePresentId';
    case 'others':
      return 'miceSchemeOtherId';
    case 'additionalItems':
      return 'id';
    case 'serviceFee':
      return 'miceSchemeServiceFeeId';
    default:
      return '';
  }
};

// 附件
const schemeFileEmit = (arr: Array<any>) => {
  schemeFileObj.value = [...arr];
};

// 补充条目
const supplementEntryEmit = (data: any) => {
  console.log('补充条目里有啥：', data);
  additionalItems.value = [...data];
  schemePriceOrQuantityChange('additionalItems');
};

// 账单附件相关事件处理
const handleHotelContractEmit = (data: any) => {
  // 保存一手合同数据，用于暂存
  attachmentContracts.value = data;
};
const handleInvoiceEmit = (data: any) => {
  invoiceList.value = data;
};

// 处理发票删除事件，清除所有关联数据
const handleInvoiceDeleted = (deletedInvoiceTempId: string) => {
  // 清除所有项目中与该发票的关联
  const fieldToUpdate = 'invoiceTempId';
  const numericId = extractNumericId(deletedInvoiceTempId);
  // 清除住宿关联
  if (schemePlanObj.value?.stays) {
    schemePlanObj.value.stays.forEach((stay: any) => {
      if (stay[fieldToUpdate] && stay[fieldToUpdate].toString() === numericId) {
        stay[fieldToUpdate] = null;
      }
    });
  }

  // 清除场地关联
  if (schemePlanObj.value?.places) {
    schemePlanObj.value.places.forEach((place: any) => {
      if (place[fieldToUpdate] && place[fieldToUpdate].toString() === numericId) {
        place[fieldToUpdate] = null;
      }
    });
  }

  // 清除餐饮关联
  if (schemePlanObj.value?.caterings) {
    schemePlanObj.value.caterings.forEach((catering: any) => {
      if (catering[fieldToUpdate] && catering[fieldToUpdate].toString() === numericId) {
        catering[fieldToUpdate] = null;
      }
    });
  }

  // 清除交通关联
  if (schemePlanObj.value?.vehicles) {
    schemePlanObj.value.vehicles.forEach((vehicle: any) => {
      if (vehicle[fieldToUpdate] && vehicle[fieldToUpdate].toString() === numericId) {
        vehicle[fieldToUpdate] = null;
      }
    });
  }

  // 清除陪同关联
  if (schemePlanObj.value?.attendants) {
    schemePlanObj.value.attendants.forEach((attendant: any) => {
      if (attendant[fieldToUpdate] && attendant[fieldToUpdate].toString() === numericId) {
        attendant[fieldToUpdate] = null;
      }
    });
  }

  // 清除活动关联
  if (schemePlanObj.value?.activities) {
    schemePlanObj.value.activities.forEach((activity: any) => {
      if (activity[fieldToUpdate] && activity[fieldToUpdate].toString() === numericId) {
        activity[fieldToUpdate] = null;
      }
    });
  }

  // 清除布展物料关联（只处理主对象）
  if (schemeMaterialObj.value?.material) {
    const schemeMaterial = schemeMaterialObj.value.material;

    // 清除主对象关联
    if (schemeMaterial[fieldToUpdate] && schemeMaterial[fieldToUpdate].toString() === numericId) {
      schemeMaterial[fieldToUpdate] = null;
    }
  }

  // 清除礼品关联
  if (schemePlanObj.value?.presents) {
    schemePlanObj.value.presents.forEach((present: any) => {
      if (present[fieldToUpdate] && present[fieldToUpdate].toString() === numericId) {
        present[fieldToUpdate] = null;
      }
      // 清除详情中的关联
      if (present.details) {
        present.details.forEach((detail: any) => {
          if (detail[fieldToUpdate] && detail[fieldToUpdate].toString() === numericId) {
            detail[fieldToUpdate] = null;
          }
        });
      }
    });
  }
  // 清除其他关联
  if (schemeOtherArr.value) {
    schemeOtherArr.value.forEach((other: any) => {
      if (other[fieldToUpdate] && other[fieldToUpdate].toString() === numericId) {
        other[fieldToUpdate] = null;
      }
    });
  }
  // 清除补充条目关联
  if (additionalItems.value) {
    additionalItems.value.forEach((additional: any) => {
      if (additional[fieldToUpdate] && additional[fieldToUpdate].toString() === numericId) {
        additional[fieldToUpdate] = null;
      }
    });
  }

  // 清除服务费关联
  if (
    schemeFeeObj.value?.serviceFee &&
    schemeFeeObj.value.serviceFee[fieldToUpdate] &&
    schemeFeeObj.value.serviceFee[fieldToUpdate].toString() === numericId
  ) {
    schemeFeeObj.value.serviceFee[fieldToUpdate] = undefined;
  }
};

// 处理水单删除事件，清除所有关联数据
const handleWaterBillDeleted = (deletedWaterBillTempId: string) => {
  // 清除所有项目中与该水单的关联
  const fieldToUpdate = 'statementTempId';
  const numericId = extractNumericId(deletedWaterBillTempId);

  // 清除住宿关联
  if (schemePlanObj.value?.stays) {
    schemePlanObj.value.stays.forEach((stay: any) => {
      if (stay[fieldToUpdate] && stay[fieldToUpdate].toString() === numericId) {
        stay[fieldToUpdate] = null;
      }
    });
  }

  // 清除场地关联
  if (schemePlanObj.value?.places) {
    schemePlanObj.value.places.forEach((place: any) => {
      if (place[fieldToUpdate] && place[fieldToUpdate].toString() === numericId) {
        place[fieldToUpdate] = null;
      }
    });
  }

  // 清除餐饮关联
  if (schemePlanObj.value?.caterings) {
    schemePlanObj.value.caterings.forEach((catering: any) => {
      if (catering[fieldToUpdate] && catering[fieldToUpdate].toString() === numericId) {
        catering[fieldToUpdate] = null;
      }
    });
  }

  // 清除交通关联
  if (schemePlanObj.value?.vehicles) {
    schemePlanObj.value.vehicles.forEach((vehicle: any) => {
      if (vehicle[fieldToUpdate] && vehicle[fieldToUpdate].toString() === numericId) {
        vehicle[fieldToUpdate] = null;
      }
    });
  }

  // 清除陪同关联
  if (schemePlanObj.value?.attendants) {
    schemePlanObj.value.attendants.forEach((attendant: any) => {
      if (attendant[fieldToUpdate] && attendant[fieldToUpdate].toString() === numericId) {
        attendant[fieldToUpdate] = null;
      }
    });
  }

  // 清除活动关联
  if (schemePlanObj.value?.activities) {
    schemePlanObj.value.activities.forEach((activity: any) => {
      if (activity[fieldToUpdate] && activity[fieldToUpdate].toString() === numericId) {
        activity[fieldToUpdate] = null;
      }
    });
  }

  // 清除布展物料关联（只处理主对象）
  if (schemeMaterialObj.value?.material) {
    const schemeMaterial = schemeMaterialObj.value.material;

    // 清除主对象关联
    if (schemeMaterial[fieldToUpdate] && schemeMaterial[fieldToUpdate].toString() === numericId) {
      schemeMaterial[fieldToUpdate] = null;
    }
  }

  // 清除礼品关联
  if (schemePlanObj.value?.presents) {
    schemePlanObj.value.presents.forEach((present: any) => {
      if (present[fieldToUpdate] && present[fieldToUpdate].toString() === numericId) {
        present[fieldToUpdate] = null;
      }
      // 清除详情中的关联
      if (present.details) {
        present.details.forEach((detail: any) => {
          if (detail[fieldToUpdate] && detail[fieldToUpdate].toString() === numericId) {
            detail[fieldToUpdate] = null;
          }
        });
      }
    });
  }

  // 清除其他关联
  if (schemeOtherArr.value) {
    schemeOtherArr.value.forEach((other: any) => {
      if (other[fieldToUpdate] && other[fieldToUpdate].toString() === numericId) {
        other[fieldToUpdate] = null;
      }
    });
  }

  // 清除服务费关联
  if (
    schemeFeeObj.value?.serviceFee &&
    schemeFeeObj.value.serviceFee[fieldToUpdate] &&
    schemeFeeObj.value.serviceFee[fieldToUpdate].toString() === numericId
  ) {
    schemeFeeObj.value.serviceFee[fieldToUpdate] = undefined;
  }
  console.log(additionalItems.value, 'additionalItems.value');

  // 清除补充条目关联
  if (additionalItems.value) {
    additionalItems.value.forEach((additional: any) => {
      if (additional[fieldToUpdate] && additional[fieldToUpdate].toString() === numericId) {
        additional[fieldToUpdate] = null;
      }
    });
  }
};

// 处理其他附件删除事件，清除所有关联数据
const handleOtherAttachmentDeleted = (deletedAttachmentTempId: string) => {
  // 清除所有项目中与该附件的关联
  const fieldToUpdate = 'otherAttachmentTempId';
  const numericId = extractNumericId(deletedAttachmentTempId);

  // 清除住宿关联
  if (schemePlanObj.value?.stays) {
    schemePlanObj.value.stays.forEach((stay: any) => {
      if (stay[fieldToUpdate] && stay[fieldToUpdate].toString() === numericId) {
        stay[fieldToUpdate] = null;
      }
    });
  }

  // 清除场地关联
  if (schemePlanObj.value?.places) {
    schemePlanObj.value.places.forEach((place: any) => {
      if (place[fieldToUpdate] && place[fieldToUpdate].toString() === numericId) {
        place[fieldToUpdate] = null;
      }
    });
  }

  // 清除餐饮关联
  if (schemePlanObj.value?.caterings) {
    schemePlanObj.value.caterings.forEach((catering: any) => {
      if (catering[fieldToUpdate] && catering[fieldToUpdate].toString() === numericId) {
        catering[fieldToUpdate] = null;
      }
    });
  }

  // 清除交通关联
  if (schemePlanObj.value?.vehicles) {
    schemePlanObj.value.vehicles.forEach((vehicle: any) => {
      if (vehicle[fieldToUpdate] && vehicle[fieldToUpdate].toString() === numericId) {
        vehicle[fieldToUpdate] = null;
      }
    });
  }

  // 清除陪同关联
  if (schemePlanObj.value?.attendants) {
    schemePlanObj.value.attendants.forEach((attendant: any) => {
      if (attendant[fieldToUpdate] && attendant[fieldToUpdate].toString() === numericId) {
        attendant[fieldToUpdate] = null;
      }
    });
  }

  // 清除活动关联
  if (schemePlanObj.value?.activities) {
    schemePlanObj.value.activities.forEach((activity: any) => {
      if (activity[fieldToUpdate] && activity[fieldToUpdate].toString() === numericId) {
        activity[fieldToUpdate] = null;
      }
    });
  }

  // 清除布展物料关联（只处理主对象）
  if (schemeMaterialObj.value?.material) {
    const schemeMaterial = schemeMaterialObj.value.material;

    // 清除主对象关联
    if (schemeMaterial[fieldToUpdate] && schemeMaterial[fieldToUpdate].toString() === numericId) {
      schemeMaterial[fieldToUpdate] = null;
    }
  }

  // 清除礼品关联
  if (schemePlanObj.value?.presents) {
    schemePlanObj.value.presents.forEach((present: any) => {
      if (present[fieldToUpdate] && present[fieldToUpdate].toString() === numericId) {
        present[fieldToUpdate] = null;
      }
      // 清除详情中的关联
      if (present.details) {
        present.details.forEach((detail: any) => {
          if (detail[fieldToUpdate] && detail[fieldToUpdate].toString() === numericId) {
            detail[fieldToUpdate] = null;
          }
        });
      }
    });
  }

  // 清除其他关联
  if (schemePlanObj.value?.others) {
    schemePlanObj.value.others.forEach((other: any) => {
      if (other[fieldToUpdate] && other[fieldToUpdate].toString() === numericId) {
        other[fieldToUpdate] = null;
      }
    });
  }

  // 清除服务费关联
  if (
    schemeFeeObj.value?.serviceFee &&
    schemeFeeObj.value.serviceFee[fieldToUpdate] &&
    schemeFeeObj.value.serviceFee[fieldToUpdate].toString() === numericId
  ) {
    schemeFeeObj.value.serviceFee[fieldToUpdate] = undefined;
  }

  // 清除补充条目关联
  if (additionalItems.value) {
    additionalItems.value.forEach((additional: any) => {
      if (additional[fieldToUpdate] && additional[fieldToUpdate].toString() === numericId) {
        additional[fieldToUpdate] = null;
      }
    });
  }
};

const handleWaterBillEmit = (data: any) => {
  waterBillList.value = data;
};

const handleAccommodationDetailEmit = (data: any) => {
  accommodationDetailList.value = data;
};

const handleConferencePhotosEmit = (data: any) => {
  conferencePhotoList.value = data;
};

const handleOtherAttachmentsEmit = (data: any) => {
  otherAttachmentList.value = data;
};

const handleInsuranceAttachmentEmit = (data: any) => {
  insuranceAttachmentList.value = data;
};

// 处理保险附件临时ID
const handleInsuranceAttachmentTempId = (data: any) => {
  // 将拼接好的附件数据传递给所有保险组件
  // 通过 schemePlanRef -> insuranceRef 的路径访问保险组件
  if (schemePlanRef.value && schemePlanRef.value.insuranceRef) {
    const insuranceComponents = schemePlanRef.value.insuranceRef;
    if (insuranceComponents && insuranceComponents.length > 0) {
      // 遍历所有保险组件实例，为每个都添加附件
      insuranceComponents.forEach((insuranceComponent, index) => {
        if (insuranceComponent && insuranceComponent.updateInsuranceAttachmentId) {
          // 传递完整的附件数据对象，索引传0（因为每个组件内部会处理所有项目）
          insuranceComponent.updateInsuranceAttachmentId(0, data);
        }
      });
    }
  }
};

// 计算实际总签到人数（基于住宿详单）
const totalCheckInPersonNum = computed(() => {
  if (!accommodationDetailList.value || accommodationDetailList.value.length === 0) {
    return 0;
  }
  return accommodationDetailList.value.reduce((total, item) => {
    return total + (item.checkInPersonNum || 0);
  }, 0);
});

// 计算真实签到人数（基于签到系统返回的count）
const realSignInPersonNum = computed(() => {
  return signInTotalCount.value || 0;
});

// 判断是否有保险数据
const hasInsuranceData = computed(() => {
  return schemeDetail.value?.insurances && schemeDetail.value.insurances.length > 0;
});

// 签到人数明细弹框
const checkInDetailVisible = ref(false);
// 签到人员数据
const signInPersonList = ref<Array<any>>([]);
const signInLoading = ref(false);
// 签到总人数（从接口返回的count字段）
const signInTotalCount = ref<number>(0);

// 签到人数明细表格列配置
const checkInColumns = [
  {
    title: '签到日期',
    dataIndex: 'checkInDate',
    key: 'checkInDate',
    width: 120,
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '房间号',
    dataIndex: 'roomNumber',
    key: 'roomNumber',
    width: 100,
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '入住人姓名',
    dataIndex: 'guestName',
    key: 'guestName',
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '签到人数',
    dataIndex: 'checkInPersonNum',
    key: 'checkInPersonNum',
    width: 100,
    align: 'center',
  },
];

// 系统签到人员表格列配置
const signInPersonColumns = [
  {
    title: '姓名',
    dataIndex: 'nickName',
    key: 'nickName',
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '手机号',
    dataIndex: 'phoneNo',
    key: 'phoneNo',
    width: 120,
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    key: 'idCard',
    width: 180,
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '会议单号',
    dataIndex: 'miceCode',
    key: 'miceCode',
    width: 120,
    align: 'center',
    customRender: ({ text }) => text || '-',
  },
];

// 查看住宿详单
const viewAccommodationDetail = () => {
  // 打开签到人数明细弹框，显示已获取的签到数据
  checkInDetailVisible.value = true;
};

// 获取签到人员列表
const fetchSignInPersonList = async (miceCode: string) => {
  try {
    signInLoading.value = true;
    const response = await meetingSignInApi.queryCheck({ miceCode });
    // 接口返回结构：{ data: { count: number, detail: [] }, success: boolean }
    signInPersonList.value = response.data?.detail || [];
    signInTotalCount.value = response.data?.count || 0;
  } catch (error) {
    message.error('获取签到人员数据失败');
    signInPersonList.value = [];
    signInTotalCount.value = 0;
  } finally {
    signInLoading.value = false;
  }
};

const handleViewRelatedBill = async (item: any, type: string) => {
  // 🔧 新增：执行数据校验（类似导出结算单的校验逻辑）
  try {
    // 1. 验证日程安排数据
    if (schemePlanRef.value && !schemePlanRef.value.SchemePlanSub()) {
      message.error('请先完善日程安排信息后再进行关联！');
      return;
    }

    // 2. 验证布展物料数据（仅当存在布展物料时）
    if (schemeDetail.value?.material?.materialDetails && schemeDetail.value?.material?.materialDetails?.length > 0) {
      if (schemeMaterialRef.value && !schemeMaterialRef.value.materialSub()) {
        message.error('请先完善布展物料信息后再进行关联！');
        return;
      }
    }

    // 3. 验证礼品数据（仅当存在礼品时）
    if (schemeDetail.value?.presents && schemeDetail.value?.presents?.length > 0) {
      if (schemePresentRef.value && !schemePresentRef.value.presentSub()) {
        message.error('请先完善礼品信息后再进行关联！');
        return;
      }
    }

    // 4. 验证其他数据（仅当存在其他项目时）
    if (schemeDetail.value?.others && schemeDetail.value?.others?.length > 0) {
      if (schemeOtherRef.value && !schemeOtherRef.value.otherSub()) {
        message.error('请先完善其他方案信息后再进行关联！');
        return;
      }
    }

    // 5. 验证全单服务费数据（仅当配置了服务费时）
    if (showFee.value) {
      if (schemeFeeRef.value && !schemeFeeRef.value.serviceFeeSub()) {
        message.error('请先完善全单服务费信息后再进行关联！');
        return;
      }
    }

    // 校验通过后，继续原有的关联逻辑
  } catch (error) {
    console.error('数据校验过程中出现错误:', error);
    message.error('数据校验失败，请检查各项信息是否完整！');
    return;
  }

  // 🔧 修复：从最新的列表中获取数据，确保 relatedBills 是最新的
  let latestBillData = item;

  // 通过 tempId 判断是发票还是水单，并获取最新数据
  const foundInvoice = invoiceList.value.find((invoice: any) => invoice.tempId === item.tempId);
  const foundWaterBill = waterBillList.value.find((waterBill: any) => waterBill.tempId === item.tempId);
  if (type == 'invoice' && foundInvoice) {
    currentBillType.value = 'invoice';
    latestBillData = foundInvoice; // 使用最新的发票数据
  } else if (foundWaterBill && type == 'waterBill') {
    currentBillType.value = 'waterBill';
    latestBillData = foundWaterBill; // 使用最新的水单数据
  } else {
    // 默认为发票类型
    currentBillType.value = 'invoice';
  }

  // 🔧 新增：传递发票/水单总金额作为初始附件金额
  currentBillData.value = {
    ...latestBillData,
    initialAttachmentAmount: latestBillData.totalAmountCny || 0, // 传递总金额给弹框
  };
  console.log(currentBillData.value, 'currentBillData.value');

  // 检查传递给弹框的完整数据结构
  demandInfo.value = {
    ...schemePlanObj.value,
    presents: schemePresentArr.value,
    others: schemeOtherArr.value,
    serviceFee: schemeFeeObj.value?.serviceFee || schemeFeeObj.value,
    additionalItems: additionalItems.value,
    schemeMaterial: schemeMaterialObj.value?.material, // 🔧 新增：布展物料数据
  };
  console.log(demandInfo.value, 'demandInfo.value');

  // 数据传递给关联弹框

  relatedBillVisible.value = true;
};

const handleRelatedBillConfirm = (data: any) => {
  relatedBillVisible.value = false;
};

// 🔧 新增：处理附件金额更新
const handleUpdateAttachmentAmount = (data: {
  invoiceTempId: string;
  billType: 'invoice' | 'waterBill';
  attachmentAmount: number;
}) => {
  if (data.billType === 'invoice') {
    // 更新发票的关联金额合计
    if (invoiceRef.value && invoiceRef.value.updateRelatedAmount) {
      invoiceRef.value.updateRelatedAmount(
        data.invoiceTempId,
        data.attachmentAmount,
        currentBillData.value?.relatedBills || [],
      );
    }
  } else if (data.billType === 'waterBill') {
    // 更新水单的关联金额合计
    if (waterBillRef.value && waterBillRef.value.updateRelatedAmount) {
      waterBillRef.value.updateRelatedAmount(
        data.invoiceTempId,
        data.attachmentAmount,
        currentBillData.value?.relatedBills || [],
      );
    }
  }

  message.success(
    `已更新${data.billType === 'invoice' ? '发票' : '水单'}关联金额：${formatNumberThousands(
      data.attachmentAmount.toFixed(2),
    )}元`,
  );
};

// 处理所有类型项目的发票/水单关联
const handleUpdateStaysInvoiceId = (data: {
  tempId: string;
  billType: 'invoice' | 'waterBill';
  selectedItemsByType: {
    stays: string[];
    places: string[];
    caterings: string[];
    vehicles: string[];
    attendants: string[];
    activities: string[];
    material: string[]; // 🔧 新增：布展物料
    others: string[]; // 🔧 新增：其他
    additionalItems: string[]; // 🔧 新增：补充条目
    serviceFee: string[]; // 🔧 新增：全单服务费
  };
}) => {
  debugger
  // 确定要更新的字段名
  const fieldToUpdate = data.billType === 'invoice' ? 'invoiceTempId' : 'statementTempId';

  // 🔧 修复：判断是添加关联、清除关联还是删除操作
  const isClearing = !data.tempId || data.tempId === '';
  const isDeleting = data.tempId && data.tempId.toString().startsWith('DELETE_');

  // 🔧 修复：如果是删除操作，提取原始的发票ID
  let actualInvoiceId = data.tempId;
  if (isDeleting) {
    actualInvoiceId = data.tempId;
  }

  const billData: IBillDetail = schemePlanObj.value as unknown as IBillDetail;

  // 将data.selectedItemsByType中有数组有值的KEY提取出来
  const selectedKeys: SchemeItemType[] = Object.keys(data.selectedItemsByType).filter(
    (key) => data.selectedItemsByType[key as SchemeItemType].length > 0,
  ) as SchemeItemType[];
  // 循环判断billData中fieldToUpdate对应的值是否存在并且等于tempId
  selectedKeys.forEach((key) => {
    // others,additionalItems,serviceFee, material不在billData中，需要单独处理
    if (key === 'others' || key === 'additionalItems' || key === 'serviceFee' || key === 'material') {
      // 根据key的值对specialData赋值
      const specialData:
        | Array<IBillDetailOther>
        | Array<IBillDetailAdditionalItem>
        | ISchemeServiceFee
        | IBillDetailMaterial
        | undefined =
        key === 'others'
          ? schemeOtherArr.value
          : key === 'additionalItems'
          ? additionalItems.value
          : key === 'serviceFee'
          ? schemeFeeObj.value?.serviceFee
          : schemeMaterialObj.value?.material;
      // 判断specialData是数组类型还是对象类型，如果是数组则循环对fieldToUpdate对应的字段赋值，如果是对象则直接对fieldToUpdate对应的字段赋值
      if (specialData) {
        if (Array.isArray(specialData)) {
          specialData.forEach((item: any) => {
            // 判断是不是已经在data.selectedItemsByType[key]中，如果在则添加删除逻辑，如果不在则添加添加逻辑
            if (
              item[fieldToUpdate] &&
              data.selectedItemsByType[key].includes(`${key + '_' + item[getSchemeIdField(key)]}`) &&
              (item[fieldToUpdate].toString() === data.tempId.toString() ||
                item[fieldToUpdate].toString() === data.tempId.replace('DELETE_', '').toString())
            ) {
              // 如果是删除的话，则将item[fieldToUpdate]设置为null
              if (isDeleting || isClearing) {
                item[fieldToUpdate] = null;
              }
            } else {
              // 如果是添加的话，则将item[fieldToUpdate]设置为tempId
              item[fieldToUpdate] = data.tempId.toString().replace('DELETE_', '');
            }
          });
        } else {
          if (
            specialData[fieldToUpdate] &&
            data.selectedItemsByType[key].includes(`${key + '_' + specialData[getSchemeIdField(key)]}`) &&
            (specialData[fieldToUpdate].toString() === data.tempId.toString() ||
              specialData[fieldToUpdate].toString() === data.tempId.replace('DELETE_', '').toString())
          ) {
            if (isDeleting || isClearing) {
              specialData[fieldToUpdate] = undefined;
            }
          } else {
            specialData[fieldToUpdate] = data.tempId.toString().replace('DELETE_', '');
          }
        }
      }
    } else {
      billData[key].forEach((item: any) => {
        if (
          item[fieldToUpdate] &&
          data.selectedItemsByType[key].includes(`${key + '_' + item[getSchemeIdField(key)]}`) &&
          (item[fieldToUpdate].toString() === data.tempId.toString() ||
            item[fieldToUpdate].toString() === data.tempId.replace('DELETE_', '').toString())
        ) {
          // 如果是删除的话，则将item[fieldToUpdate]设置为null
          if (isDeleting || isClearing) {
            item[fieldToUpdate] = null;
          }
        } else {
          // 如果是添加的话，则将item[fieldToUpdate]设置为tempId
          item[fieldToUpdate] = data.tempId.toString().replace('DELETE_', '');
        }
      });
    }
  });

  // 🔧 修复：触发所有相关数据的更新事件
  schemePlanEmit(schemePlanObj.value);

  // 触发布展物料数据更新
  if (data.selectedItemsByType.material.length > 0) {
    schemeMaterialEmit(schemeMaterialObj.value);
  }

  // 触发其他方案数据更新
  if (data.selectedItemsByType.others.length > 0) {
    schemeOtherEmit(schemeOtherArr.value);
  }

  // 触发全单服务费数据更新
  if (data.selectedItemsByType.serviceFee.length > 0) {
    // 🔧 修复：保存关联ID，防止被 emit 覆盖
    const savedInvoiceTempId = schemeFeeObj.value?.serviceFee?.invoiceTempId;
    const savedStatementTempId = schemeFeeObj.value?.serviceFee?.statementTempId;

    schemeFeeEmit(schemeFeeObj.value);

    // 🔧 修复：emit 后恢复关联ID
    if (schemeFeeObj.value?.serviceFee) {
      if (savedInvoiceTempId !== undefined) {
        schemeFeeObj.value.serviceFee.invoiceTempId = savedInvoiceTempId;
      }
      if (savedStatementTempId !== undefined) {
        schemeFeeObj.value.serviceFee.statementTempId = savedStatementTempId;
      }
    }
  }

  // 触发补充条目数据更新
  supplementEntryEmit(additionalItems.value);
};

// 处理关联金额更新
// 获取结算单信息
const getSettlementBalances = () => {
  const balances = [];

  // 🔧 修复：正确访问ref值的方式
  if (ExportExpenseConfirmationRef.value && ExportExpenseConfirmationRef.value.hasImportedSettlement) {
    const settlementInfo = {
      subType: schemeDetail.value?.miceName || '结算单项目', // 子类型，使用动态的项目名称
      balanceAttachment: ExportExpenseConfirmationRef.value.importedFilePath
        ? [ExportExpenseConfirmationRef.value.importedFilePath]
        : [], // 结算单附件路径数组
    };

    balances.push(settlementInfo);
  } else {
  }
  return balances;
};

const handleUpdateRelatedAmount = (data: {
  invoiceTempId: string;
  billType: 'invoice' | 'waterBill';
  totalAmount: number;
  relatedBills: any[];
}) => {
  if (data.billType === 'invoice') {
    // 更新发票的关联金额合计和关联账单数据
    if (invoiceRef.value && invoiceRef.value.updateRelatedAmount) {
      invoiceRef.value.updateRelatedAmount(data.invoiceTempId, data.totalAmount, data.relatedBills);
    }
  } else if (data.billType === 'waterBill') {
    // 更新水单的关联金额合计和关联账单数据
    if (waterBillRef.value && waterBillRef.value.updateRelatedAmount) {
      waterBillRef.value.updateRelatedAmount(data.invoiceTempId, data.totalAmount, data.relatedBills);
    }
  }

  // todo:hdx 这里需要调试看一下
  // 🔧 新增：更新补充条目的关联字段
  updateAdditionalItemsRelation(data.invoiceTempId, data.billType, data.relatedBills);

  // 🔧 修复：移除这里的成功提示，因为关联账单弹框已经有了更准确的提示
  // message.success(
  //   `已更新${data.billType === 'invoice' ? '发票' : '水单'}关联金额合计：${formatNumberThousands(data.totalAmount)}元，关联账单：${data.relatedBills.length
  //   }条`,
  // );
};

// 🔧 新增：更新补充条目的关联字段
const updateAdditionalItemsRelation = (billTempId: string, billType: 'invoice' | 'waterBill', relatedBills: any[]) => {
  if (!additionalItems.value) return;

  const fieldToUpdate = billType === 'invoice' ? 'invoiceTempId' : 'statementTempId';

  // 找出关联的补充条目ID
  const relatedAdditionalItemIds = relatedBills
    .filter((bill) => (bill.originalId || bill.id).startsWith('additionalItem_'))
    .map((bill) => bill.originalId || bill.id);
  // 更新补充条目的关联字段
  additionalItems.value.forEach((item: any) => {
    // 🔧 修复：tempId已经包含前缀，不需要重复添加
    const itemId = item.tempId || `additionalItem_${item.id || Date.now()}`;

    if (relatedAdditionalItemIds.includes(itemId)) {
      // 设置关联字段
      const numericId = extractNumericId(billTempId);
      item[fieldToUpdate] = numericId;
    } else {
      // 如果当前补充条目之前关联了这个账单，但现在不在关联列表中，则清除关联
      if (item[fieldToUpdate] === extractNumericId(billTempId)) {
        item[fieldToUpdate] = null;
      }
    }
  });
  debugger
  // 通知补充条目组件数据已更新
  if (supplementEntryRef.value && supplementEntryRef.value.emitData) {
    supplementEntryRef.value.emitData();
  }
};

// 提取原始账单ID的辅助函数
const extractOriginalBillId = (billId: string): string => {
  // 如果是新添加的格式 'new_timestamp_originalId'，提取原始ID
  if (billId.startsWith('new_')) {
    const parts = billId.split('_');
    if (parts.length >= 3) {
      return parts.slice(2).join('_'); // 去掉 'new_' 和时间戳部分
    }
  }
  return billId;
};

// 提取发票/水单ID中的数字部分
const extractNumericId = (tempId: string): string => {
  // 🔧 修复：如果已经是纯数字格式，直接返回
  if (tempId && /^\d+$/.test(tempId)) {
    return tempId;
  }

  // 从 'invoice_1753756809308_326' 格式中提取 '1753756809308'
  if (tempId && tempId.includes('_')) {
    const parts = tempId.split('_');
    if (parts.length >= 2) {
      // 返回第二部分（时间戳数字）
      return parts[1];
    }
  }
  return tempId;
};

// 🔧 新增：恢复响应式变量的数据（关联弹框需要这些数据）
const restoreReactiveVariables = () => {
  if (!billDetail.value) {
    return;
  }

  // 恢复 schemePlanObj 数据
  if (
    billDetail.value.stays ||
    billDetail.value.places ||
    billDetail.value.caterings ||
    billDetail.value.vehicles ||
    billDetail.value.attendants ||
    billDetail.value.activities
  ) {
    schemePlanObj.value = {
      stays: billDetail.value.stays || [],
      places: billDetail.value.places || [],
      caterings: billDetail.value.caterings || [],
      vehicles: billDetail.value.vehicles || [],
      attendants: billDetail.value.attendants || [],
      activities: billDetail.value.activities || [],
      insurances: billDetail.value.insurances || [],
      serviceFee: billDetail.value.serviceFee || [],
      additionalItems: billDetail.value.additionalItems || [],
    };
  }

  // 🔧 恢复 schemeMaterialObj 数据
  if (billDetail.value.material) {
    // 🔧 确保包含完整的数据结构，特别是 materialDetails 字段
    const materialData = {
      ...billDetail.value.material,
      // 🔧 确保 materialDetails 字段存在，如果缓存中没有则使用空数组
      materialDetails: billDetail.value.material.materialDetails || [],
    };

    schemeMaterialObj.value = {
      material: materialData,
    };
  }

  // 恢复 schemePresentArr 数据
  if (billDetail.value.presents) {
    schemePresentArr.value = [...billDetail.value.presents];
  }

  // 恢复 schemeOtherArr 数据
  if (billDetail.value.others) {
    schemeOtherArr.value = [...billDetail.value.others];
  }

  // 恢复 schemeFeeObj 数据
  if (billDetail.value.serviceFee) {
    schemeFeeObj.value = {
      serviceFee: billDetail.value.serviceFee,
    };
  }

  // 恢复 additionalItems 数据
  if (billDetail.value.additionalItems) {
    additionalItems.value = [...billDetail.value.additionalItems];
  }
};

// 🔧 新增：清理无效的关联ID
const cleanInvalidRelationIds = () => {
  // 获取当前有效的发票和水单ID列表
  const validInvoiceIds = (invoiceList.value || [])
    .map((invoice: any) => extractNumericId(invoice.tempId))
    .filter(Boolean);
  const validWaterBillIds = (waterBillList.value || [])
    .map((waterBill: any) => extractNumericId(waterBill.tempId))
    .filter(Boolean);

  const invoiceIdSet = new Set(validInvoiceIds.map(String));
  const waterBillIdSet = new Set(validWaterBillIds.map(String));

  // 🔧 如果没有发票或水单，则不进行清理（可能是数据还没加载完）
  if (validInvoiceIds.length === 0 && validWaterBillIds.length === 0) {
    return;
  }

  // 清理住宿数据中的无效关联ID
  if (billDetail.value.stays && Array.isArray(billDetail.value.stays)) {
    billDetail.value.stays.forEach((stay: any, index: number) => {
      let cleaned = false;

      // 清理无效的发票关联
      if (stay.invoiceTempId && !invoiceIdSet.has(String(stay.invoiceTempId))) {
        stay.invoiceTempId = null;
        cleaned = true;
      }

      // 清理无效的水单关联
      if (stay.statementTempId && !waterBillIdSet.has(String(stay.statementTempId))) {
        stay.statementTempId = null;
        cleaned = true;
      }
    });
  }

  // 🔧 清理布展物料数据中的无效关联ID（只处理主对象）
  if (billDetail.value.material) {
    let cleaned = false;

    // 清理主对象的无效关联ID
    if (
      billDetail.value.material.invoiceTempId &&
      !invoiceIdSet.has(billDetail.value.material.invoiceTempId.toString())
    ) {
      billDetail.value.material.invoiceTempId = null;
      cleaned = true;
    }

    if (
      billDetail.value.material.statementTempId &&
      !waterBillIdSet.has(billDetail.value.material.statementTempId.toString())
    ) {
      billDetail.value.material.statementTempId = null;
      cleaned = true;
    }
  }

  // 清理其他业务数据的无效关联ID（会场、用餐、用车等）
  const businessDataTypes = ['places', 'caterings', 'vehicles', 'attendants', 'activities', 'presents', 'others'];

  businessDataTypes.forEach((dataType) => {
    if (billDetail.value[dataType] && Array.isArray(billDetail.value[dataType])) {
      billDetail.value[dataType].forEach((item: any, index: number) => {
        let cleaned = false;

        if (item.invoiceTempId && !invoiceIdSet.has(item.invoiceTempId.toString())) {
          item.invoiceTempId = null;
          cleaned = true;
        }

        if (item.statementTempId && !waterBillIdSet.has(item.statementTempId.toString())) {
          item.statementTempId = null;
          cleaned = true;
        }
      });
    }
  });

  // 清理服务费的无效关联ID
  if (billDetail.value.serviceFee) {
    let cleaned = false;

    if (
      billDetail.value.serviceFee.invoiceTempId &&
      !invoiceIdSet.has(billDetail.value.serviceFee.invoiceTempId!.toString())
    ) {
      billDetail.value.serviceFee.invoiceTempId = null;
      cleaned = true;
    }

    if (
      billDetail.value.serviceFee.statementTempId &&
      !waterBillIdSet.has(billDetail.value.serviceFee.statementTempId.toString())
    ) {
      billDetail.value.serviceFee.statementTempId = null;
      cleaned = true;
    }
  }

  // 清理补充条目的无效关联ID
  if (billDetail.value.additionalItems && Array.isArray(billDetail.value.additionalItems)) {
    billDetail.value.additionalItems.forEach((item: any, index: number) => {
      let cleaned = false;

      if (item.invoiceTempId && !invoiceIdSet.has(item.invoiceTempId.toString())) {
        item.invoiceTempId = null;
        cleaned = true;
      }

      if (item.statementTempId && !waterBillIdSet.has(item.statementTempId.toString())) {
        item.statementTempId = null;
        cleaned = true;
      }
    });
  }
};

// 🔧 新增：从缓存数据重建发票关联关系
const rebuildInvoiceRelationshipsFromCache = () => {
  if (!invoiceList.value || invoiceList.value.length === 0) {
    return;
  }

  buildAssociatedData('invoiceTempId');
};

// 构建key判断是水单还是发票，构建已关联账单弹窗数据
const buildAssociatedData = (key: 'invoiceTempId' | 'statementTempId') => {
  // 暂时只给获取详情页使用
  let sourceData: IBillDetailAttachmentInvoice[] | IBillDetailAttachmentStatement[] = [];
  if (key === 'invoiceTempId') {
    sourceData = invoiceList.value;
  } else {
    sourceData = waterBillList.value;
  }

  if (sourceData.length === 0) {
    return;
  }

  sourceData.forEach((item: IBillDetailAttachmentInvoice | IBillDetailAttachmentStatement) => {
    if (!item.tempId) return;
    const tempId = item.tempId;
    const relatedBills: IBillAssociatedType[] = [];
    let totalRelatedAmount = 0;

    // 1. 检查住宿关联
    if (billDetail.value.stays && Array.isArray(billDetail.value.stays)) {
      billDetail.value.stays.forEach((stay: IBillDetailStay, index: number) => {
        // 🔧 修复：只有当关联ID确实存在且不为空时才建立关联
        if (stay[key] && stay[key] == tempId) {
          const stayId = `stays_${stay.miceSchemeStayId}`;

          // 🔧 修复：安全的金额计算，防止负数或异常值
          const billRoomNum = Number(stay.billRoomNum) || 0;
          const billUnitPrice = Number(stay.billUnitPrice) || 0;
          const amount = Math.max(0, billRoomNum * billUnitPrice); // 确保金额不为负数
          const stayBill: IBillAssociatedType = {
            id: stayId,
            originalId: stayId,
            name: '原需求', // 🔧 修复：统一使用简洁的"原需求"格式，与关联弹框保持一致
            amount: amount,
            type: 'stays',
            itemData: stay,
            billPrice: amount,
            billQuantity: billRoomNum,
            billUnitPrice: billUnitPrice,
          };

          relatedBills.push(stayBill);
          totalRelatedAmount += stayBill.amount;
        }
      });
    }

    // 2. 检查会场关联
    if (billDetail.value.places && Array.isArray(billDetail.value.places)) {
      billDetail.value.places.forEach((place: IBillDetailPlace, index: number) => {
        if (place[key] && place[key] == tempId) {
          const placeId = `places_${place.miceSchemePlaceId}`;

          // 🔧 修复：安全的金额计算，防止负数或异常值
          const billUnitPlacePrice = Number(place.billUnitPlacePrice) || 0;
          const ledPrice = place.hasLed ? (Number(place.billUnitLedPrice) || 0) * (Number(place.billLedNum) || 0) : 0;
          const teaPrice = place.hasTea
            ? (Number(place.billUnitTeaPrice) || 0) * (Number(place.billPersonNum) || 0)
            : 0;
          const amount = Math.max(0, billUnitPlacePrice + ledPrice + teaPrice); // 确保金额不为负数

          const placeBill: IBillAssociatedType = {
            id: placeId,
            originalId: placeId,
            name: `原需求`,
            amount: amount,
            type: 'places',
            itemData: place,
            billPrice: amount,
            billQuantity: 1,
            billUnitPrice: amount,
          };
          relatedBills.push(placeBill);
          totalRelatedAmount += placeBill.amount;
        }
      });
    }

    // 3. 检查用餐关联
    if (billDetail.value.caterings && Array.isArray(billDetail.value.caterings)) {
      billDetail.value.caterings.forEach((catering: IBillDetailCatering, index: number) => {
        if (catering[key] && catering[key] == tempId) {
          const cateringId = `caterings_${catering.miceSchemeCateringId}`;

          // 🔧 修复：安全的金额计算，防止负数或异常值
          const billUnitPrice = Number(catering.billUnitPrice) || 0;
          const billPersonNum = Number(catering.billPersonNum) || 0;
          const cateringAmount = Math.max(0, billUnitPrice * billPersonNum);

          const cateringBill: IBillAssociatedType = {
            id: cateringId,
            originalId: cateringId,
            name: `原需求`,
            amount: cateringAmount,
            type: 'caterings',
            itemData: catering,
            billPrice: cateringAmount,
            billQuantity: billPersonNum,
            billUnitPrice: billUnitPrice,
          };
          relatedBills.push(cateringBill);
          totalRelatedAmount += cateringBill.amount;
        }
      });
    }

    // 4. 检查用车关联
    if (billDetail.value.vehicles && Array.isArray(billDetail.value.vehicles)) {
      billDetail.value.vehicles.forEach((vehicle: IBillDetailVehicle, index: number) => {
        if (vehicle[key] && vehicle[key] == tempId) {
          const vehicleId = `vehicles_${vehicle.miceSchemeVehicleId}`;

          // 🔧 修复：安全的金额计算，防止负数或异常值
          const vehicleUnitPrice = Number(vehicle.billUnitPrice) || 0;
          const vehicleNum = Number(vehicle.billVehicleNum) || 0;
          const vehicleAmount = Math.max(0, vehicleUnitPrice * vehicleNum);

          const vehicleBill: IBillAssociatedType = {
            id: vehicleId,
            originalId: vehicleId,
            name: `原需求`,
            amount: vehicleAmount,
            type: 'vehicles',
            itemData: vehicle,
            billPrice: vehicleAmount,
            billQuantity: vehicleNum,
            billUnitPrice: vehicleUnitPrice,
          };
          relatedBills.push(vehicleBill);
          totalRelatedAmount += vehicleBill.amount;
        }
      });
    }

    // 5. 检查服务人员关联
    if (billDetail.value.attendants && Array.isArray(billDetail.value.attendants)) {
      billDetail.value.attendants.forEach((attendant: IBillDetailAttendant, index: number) => {
        if (attendant[key] && attendant[key] == tempId) {
          const attendantId = `attendants_${attendant.miceSchemeAttendantId}`;

          // 🔧 修复：安全的金额计算，防止负数或异常值
          const attendantUnitPrice = Number(attendant.billUnitPrice) || 0;
          const attendantPersonNum = Number(attendant.billPersonNum) || 0;
          const attendantAmount = Math.max(0, attendantUnitPrice * attendantPersonNum);

          const attendantBill: IBillAssociatedType = {
            id: attendantId,
            originalId: attendantId,
            name: `原需求`,
            amount: attendantAmount,
            type: 'attendants',
            itemData: attendant,
            billPrice: attendantAmount,
            billQuantity: attendantPersonNum,
            billUnitPrice: attendantUnitPrice,
          };
          relatedBills.push(attendantBill);
          totalRelatedAmount += attendantBill.amount;
        }
      });
    }

    // 6. 检查活动关联
    if (billDetail.value.activities && Array.isArray(billDetail.value.activities)) {
      billDetail.value.activities.forEach((activity: IBillDetailActivity, index: number) => {
        if (activity[key] && activity[key] == tempId) {
          const activityId = `activities_${activity.miceSchemeActivityId}`;

          // 🔧 修复：安全的金额计算，防止负数或异常值
          const activityUnitPrice = Number(activity.billUnitPrice) || 0;
          const activityPersonNum = Number(activity.billPersonNum) || 0;
          const activityAmount = Math.max(0, activityUnitPrice * activityPersonNum);

          const activityBill: IBillAssociatedType = {
            id: activityId,
            originalId: activityId,
            name: `原需求`,
            amount: activityAmount,
            type: 'activities',
            itemData: activity,
            billPrice: activityAmount,
            billQuantity: activityPersonNum,
            billUnitPrice: activityUnitPrice,
          };
          relatedBills.push(activityBill);
          totalRelatedAmount += activityBill.amount;
        }
      });
    }

    // 7. 检查其他项目关联
    if (billDetail.value.others && Array.isArray(billDetail.value.others)) {
      billDetail.value.others.forEach((other: IBillDetailOther, index: number) => {
        if (other[key] && other[key] == tempId) {
          const otherId = `others_${other.miceSchemeOtherId}`;

          // 🔧 修复：安全的金额计算，防止负数或异常值
          const otherTotalPrice = Number(other.billTotalPrice) || 0;
          const otherAmount = Math.max(0, otherTotalPrice);

          const otherBill: IBillAssociatedType = {
            id: otherId,
            originalId: otherId,
            name: `原需求`,
            amount: otherAmount,
            type: 'others',
            itemData: other,
            billPrice: otherAmount,
            billQuantity: 1,
            billUnitPrice: otherAmount,
          };
          relatedBills.push(otherBill);
          totalRelatedAmount += otherBill.amount;
        }
      });
    }

    // 8. 检查服务费关联
    if (
      billDetail.value.serviceFee &&
      billDetail.value.serviceFee[key] &&
      billDetail.value.serviceFee[key] == tempId
    ) {
      const serviceFeeBill: IBillAssociatedType = {
        id: `serviceFee_${billDetail.value.serviceFee.miceSchemeServiceFeeId}`,
        originalId: `serviceFee_${billDetail.value.serviceFee.miceSchemeServiceFeeId}`,
        name: '原需求',
        amount: billDetail.value.serviceFee.billServiceFeeReal,
        type: 'serviceFee',
        itemData: billDetail.value.serviceFee,
        billPrice: billDetail.value.serviceFee.billServiceFeeReal,
        billQuantity: 1,
        billUnitPrice: billDetail.value.serviceFee.billServiceFeeReal,
      };
      relatedBills.push(serviceFeeBill);
      totalRelatedAmount += serviceFeeBill.amount;
    }

    // 9. 检查补充条目关联
    if (billDetail.value.additionalItems && Array.isArray(billDetail.value.additionalItems)) {
      billDetail.value.additionalItems.forEach((item: IBillDetailAdditionalItem, index: number) => {
        if (item[key] && item[key] == tempId) {
          // 🔧 修复：安全的金额计算，防止负数或异常值
          const billNum = Number(item.billNum) || 0;
          const billUnitPrice = Number(item.billUnitPrice) || 0;
          const additionalAmount = Math.max(0, billNum * billUnitPrice);

          const additionalBill: IBillAssociatedType = {
            id: `additionalItems_${item.id}`,
            originalId: `additionalItems_${item.id}`,
            name: `原需求`,
            amount: additionalAmount,
            type: 'additionalItems',
            itemData: item,
            billPrice: additionalAmount,
            billQuantity: billNum,
            billUnitPrice: billUnitPrice,
          };
          relatedBills.push(additionalBill);
          totalRelatedAmount += additionalBill.amount;
        }
      });
    }

    // 更新发票的关联信息
    item.relatedBills = relatedBills;
    item.relatedAmountTotalCny = totalRelatedAmount;
    item.relatedBill = '查看关联>>';
  });

  //重新触发当前已关联的账单
  getAllRelatedBillIds(key);
  // 直接触发 emit 事件，更新父组件的数据
  key === 'invoiceTempId' ? handleInvoiceEmit(sourceData) : handleWaterBillEmit(sourceData);
};

// 🔧 新增：从缓存数据重建水单关联关系
const rebuildWaterBillRelationshipsFromCache = () => {
  if (!waterBillList.value || waterBillList.value.length === 0) {
    return;
  }
  buildAssociatedData('statementTempId');
};

// 处理提交数据中的所有invoiceTempId和statementTempId字段
const processSubmitDataIds = (data: any): any => {
  if (!data) return data;

  // 如果是数组，递归处理每个元素
  if (Array.isArray(data)) {
    return data.map((item) => processSubmitDataIds(item));
  }

  // 如果是对象，处理其属性
  if (typeof data === 'object') {
    const processedData = { ...data };

    // 处理invoiceTempId字段
    if (processedData.invoiceTempId && typeof processedData.invoiceTempId === 'string') {
      processedData.invoiceTempId = extractNumericId(processedData.invoiceTempId);
    }

    // 处理statementTempId字段
    if (processedData.statementTempId && typeof processedData.statementTempId === 'string') {
      processedData.statementTempId = extractNumericId(processedData.statementTempId);
    }

    // 递归处理嵌套对象
    Object.keys(processedData).forEach((key) => {
      if (typeof processedData[key] === 'object') {
        processedData[key] = processSubmitDataIds(processedData[key]);
      }
    });

    return processedData;
  }

  return data;
};

// 🔧 修复：收集同类型已关联的账单ID（发票只排除其他发票的关联，水单只排除其他水单的关联）
const getAllRelatedBillIds = (TempIdType?: 'invoiceTempId' | 'statementTempId' | undefined): string[] => {
  debugger
  const relatedBillIds: string[] = [];
  let fieldToCheck: 'invoiceTempId' | 'statementTempId';
  if (!TempIdType) {
    // 判断当前是发票还是水单
    const isInvoice = currentBillType.value === 'invoice';
    fieldToCheck = isInvoice ? 'invoiceTempId' : 'statementTempId';
  } else {
    fieldToCheck = TempIdType;
  }

  const billData: IBillDetail = schemePlanObj.value as unknown as IBillDetail;
  const selectedKeys: SchemeItemType[] = Object.keys(billData).filter(
    (key) => Array.isArray(billData[key as SchemeItemType]) ? (billData[key as SchemeItemType] as any[])?.length > 0 : billData[key as SchemeItemType] !== undefined,
  ) as SchemeItemType[];
  // 往selectedKeys中追加others,additionalItems,serviceFee, material
  selectedKeys.push('others', 'additionalItems', 'serviceFee', 'material');
  selectedKeys.forEach((key: SchemeItemType) => {
    console.log(key, 'key------>');
    let keyValue = '';
    // others,additionalItems,serviceFee, material不在billData中，需要单独处理
    if (key === 'others' || key === 'additionalItems' || key === 'serviceFee' || key === 'material') {
      // 根据key的值对specialData赋值
      const specialData:
        | Array<IBillDetailOther>
        | Array<IBillDetailAdditionalItem>
        | ISchemeServiceFee
        | IBillDetailMaterial
        | undefined =
        key === 'others'
          ? schemeOtherArr.value
          : key === 'additionalItems'
          ? additionalItems.value
          : key === 'serviceFee'
          ? schemeFeeObj.value?.serviceFee
          : schemeMaterialObj.value?.material;
      // 判断specialData是数组类型还是对象类型，如果是数组则循环对fieldToUpdate对应的字段赋值，如果是对象则直接对fieldToUpdate对应的字段赋值
      if (specialData) {
        if (Array.isArray(specialData)) {
          specialData.forEach((item: any) => {
            if (item[fieldToCheck]) {
              keyValue = `${key}_${item[getSchemeIdField(key)]}`;
              relatedBillIds.push(keyValue);
            }
          });
        } else {
          if (specialData[fieldToCheck]) {
            keyValue = `${key}_${specialData[getSchemeIdField(key)]}`;
            relatedBillIds.push(keyValue);
          }
        }
      }
    } else {
      billData[key].forEach((item: any) => {
        // 判断fieldToCheck对应的值是否为空，如果有值再添加到relatedBillIds中
        if (item[fieldToCheck]) {
          // 生成key的值
          const keyValue = `${key}_${item[getSchemeIdField(key)]}`;
          relatedBillIds.push(keyValue);
        }
      });
    }
  });

  return relatedBillIds;
};

// 账单附件（保持兼容性）
const billAttachmentEmit = (data: any) => {
  // Account bill attachment data processing
};

// 每日计划-方案金额
const planPriceEmit = (priceNum: number) => {
  planPrice.value = priceNum;

  totalPriceFn();
};
// 每日计划-各单项-方案金额
const planEachPriceEmit = (arr: Array<any>) => {
  planEachPriceList.value = arr;
};
// 布展物料-方案金额
const materialPriceEmit = (priceNum: number) => {
  materialPrice.value = priceNum;

  totalPriceFn();
};
// 礼品-方案金额
const presentPriceEmit = (priceNum: number) => {
  presentPrice.value = priceNum;

  totalPriceFn();
};
// 其他-方案金额
const otherPriceEmit = (priceNum: number) => {
  otherPrice.value = priceNum;

  totalPriceFn();
};
// 全单服务费方案 - 总金额
const totalPriceFn = debounce(() => {
  totalPrice.value = planPrice.value + materialPrice.value + presentPrice.value + otherPrice.value;

  // 方案暂存 - 合计
  handleTotal();
}, 300);

const handleTotal = debounce(() => {
  // 方案暂存 - 合计
  schemeTemporarily('total');
}, 300);

// 1min自动保存
const countDownOneMin = () => {
  // todo:hdx 开发暂时注释
  // autoSave.value = setInterval(() => {
  //   if (schemeType.value !== 'notBidding' && schemeType.value !== 'biddingView') {
  //     // 方案暂存
  //     schemeTemporarily('auto');
  //   }
  // }, 60000) as unknown as number;
  // countdownTimer.value = setInterval(() => {
  //   countdownTime.value = countdownTime.value === 0 ? 60 : countdownTime.value - 1;
  // }, 1000) as unknown as number;
};

// 缓存删除
const delCache = async () => {
  if (!miceId.value) {
    return;
  }

  const cacheKey =
    'haierbusiness-mice-merchant_' +
    loginUser.value?.username +
    '_billUploadKey' +
    miceId.value +
    '_merchantId' +
    merchantId.value;

  delData({
    applicationCode: 'haierbusiness-mice-merchant',
    cacheKey: cacheKey, // 方案互动
  });
};

// 清除所有数据
const clearAllData = () => {
  Modal.confirm({
    title: '确认清除',
    content: '确定要清除所有已填写的数据吗？此操作不可恢复。',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 删除缓存
        await delCache();

        // 重新加载页面数据，清空所有表单
        window.location.reload();

        message.success('数据已清除');
      } catch (error) {
        message.error('清除数据失败');
      }
    },
  });
};

// 暂存
const schemeTemporarily = async (type) => {
  if (!miceId.value) {
    message.error('暂存失败，会议不存在！');
    return;
  }

  // 日程安排
  schemePlanRef.value && schemePlanRef.value.schemePlanTempSave();

  // 布展物料
  schemeMaterialRef.value && schemeMaterialRef.value.materialTempSave();

  // 礼品
  schemePresentRef.value && schemePresentRef.value.presentTempSave();

  // 其他
  schemeOtherRef.value && schemeOtherRef.value.otherTempSave();

  // 全单服务费
  schemeFeeRef.value && schemeFeeRef.value.serviceFeeTempSave();

  // 账单上传相关组件暂存
  supplementEntryRef.value && supplementEntryRef.value.supplementEntryTempSave();

  // 其他附件
  otherAttachmentsRef.value && otherAttachmentsRef.value.otherAttachmentsTempSave();

  const schemeAllPrice =
    Number(
      totalPrice.value +
        (schemeFeeObj.value.serviceFee && schemeFeeObj.value.serviceFee.schemeServiceFeeReal
          ? schemeFeeObj.value.serviceFee.schemeServiceFeeReal
          : 0),
    ) || 0;

  const otherAttachments = otherAttachmentList.value
      .map((item) => ({
        subType: item.subType, // 子类型，使用附件说明作为子类型
        paths: item.paths, // 附件路径数组
      }))

  // 只把账单上传能改变的值传进去，其他的还是复用接口返回的值
  const params: IBillDetail = {
    ...billDetail.value,
    miceId: miceId.value!,
    miceSchemeId: schemeDetail.value?.id!,
    startDate: schemeDetail.value.startDate!,
    endDate: schemeDetail.value.endDate!,
    billTotalPrice: Number(schemeAllPrice.toFixed(2)), // 账单总金额
    hotels: [...hotelList.value],
    stays: schemePlanObj.value.stays as unknown as IBillDetailStay[],
    caterings: schemePlanObj.value.caterings as unknown as IBillDetailCatering[],
    places: schemePlanObj.value.places as unknown as IBillDetailPlace[],
    vehicles: schemePlanObj.value.vehicles as unknown as IBillDetailVehicle[],
    attendants: schemePlanObj.value.attendants as unknown as IBillDetailAttendant[],
    activities: schemePlanObj.value.activities as unknown as IBillDetailActivity[],
    insurances: schemePlanObj.value.insurances as unknown as IBillDetailInsurance[],
    presents: schemePlanObj.value.presents as unknown as IBillDetailPresent[],
    material: schemeMaterialObj.value?.material as unknown as IBillDetailMaterial,
    traffic: schemePlanObj.value.traffic as unknown as IBillDetailTraffic,
    others: schemeOtherArr.value as unknown as IBillDetailOther[],
    serviceFee: schemeFeeObj.value.serviceFee as unknown as IBillDetailServiceFee,
    additionalItems: additionalItems.value as unknown as IBillDetailAdditionalItem[], // 补充条目
    attachmentContracts: attachmentContracts.value as unknown as IBillDetailAttachmentContract[], // 一手合同附件
    attachmentInvoices: invoiceRef.value ? invoiceRef.value.getInvoiceDataForCache() : [], // 账单附件发票信息（过滤后）
    attachmentStatements: waterBillRef.value ? waterBillRef.value.getWaterBillDataForCache() : [], // 水单
    attachmentStayChecks: accommodationDetailRef.value ? (accommodationDetailRef.value as any).getSubmitData() : [], // 住宿详单
    attachmentPhotos: conferencePhotoList.value
      .map((item) => ({
        subType: item.subType, // 类型
        paths: item.paths, // 附件路径数组
      }))
      .filter((item) => item.subType && item.paths.length > 0), // 过滤掉没有类型或没有文件的项
    attachmentOthers: otherAttachments, // 过滤掉没有说明或没有文件的项
    balances: ExportExpenseConfirmationRef.value ? getSettlementBalances() : [], // 费用确认导出的结算单信息
  };
  debugger

  if (type === 'total') {
    // 合计
    schemeTotalInfo.value = params || {};
    return;
  }

  // 处理暂存数据中的ID格式
  const processedParams = processSubmitDataIds(params);
  console.log('暂存的数据：', processedParams);
  // 后端缓存

  const cacheKey =
    'haierbusiness-mice-merchant_' +
    loginUser.value?.username +
    '_billUploadKey' +
    miceId.value +
    '_merchantId' +
    merchantId.value;
  const res = await saveDataBy({
    applicationCode: 'haierbusiness-mice-merchant',
    // 规则: haierbusiness-mice-bid_工号_你业务的缓存key
    cacheKey: cacheKey, // 方案互动
    cacheValue: JSON.stringify({
      ...processedParams,
    }),
  });

  if (res && type === 'hand') {
    // 手动保存
    message.success('账单上传已暂存！');
  }
};

//比较总计金额，与发票，水单，补充条目之间的逻辑
//1.首先拿总金额
const hanleTotalAmount = (value: number) => {
  totalAmount.value = value;
};
//2.拿发票，水单，补充条目的金额
const calculateAmount = () => {
  //发票
  let invoiceAmount = 0;
  //水单
  let WaterBillAmount = 0;
  //补充条目
  let supplementaryAmount = 0;
  //补充计算
  if (additionalItems.value && additionalItems.value.length > 0) {
    for (const item of additionalItems.value) {
      if (!item.billNum || !item.billUnitPrice) continue;
      supplementaryAmount += Number((item.billNum * item.billUnitPrice).toFixed(2));
    }
  }
  //发票计算
  if (invoiceList.value && invoiceList.value.length > 0) {
    invoiceList.value.forEach((item) => {
      invoiceAmount += Number(item.relatedAmountTotalCny.toFixed(2));
    });
  }
  //水单计算
  if (waterBillList.value && waterBillList.value.length > 0) {
    waterBillList.value.forEach((item) => {
      WaterBillAmount += Number(item.relatedAmountTotalCny.toFixed(2));
    });
  }

  if (totalAmount.value && invoiceAmount != 0 && WaterBillAmount != 0) {
    //总计 + 补充
    const totalPrice = Number((Number(totalAmount.value) + Number(supplementaryAmount)).toFixed(2));
    if (totalPrice) {
      if (totalPrice != invoiceAmount || totalPrice != WaterBillAmount) {
        Modal.warning({
          title: '关联账单金额与总计金额不相等，请重新规划',
          okText: '确定',
        });
        return false;
      }
    } else {
      message.error('账单金额计算错误');
      return false;
    }
  }
  return true;
};

const restoreRelationData = (relationData: any) => {
  // 恢复住宿关联数据
  if (schemePlanObj.value?.stays && relationData.stays.length > 0) {
    schemePlanObj.value.stays.forEach((stay: any) => {
      const stayId = stay.id || stay.tempId || stay.miceDemandStayId || stay.miceSchemeStayId;
      const savedData = relationData.stays.find((item: any) => item.id === stayId);
      if (savedData) {
        stay.invoiceTempId = savedData.invoiceTempId;
        stay.statementTempId = savedData.statementTempId;
      }
    });
  }

  // 恢复会场关联数据
  if (schemePlanObj.value?.places && relationData.places.length > 0) {
    schemePlanObj.value.places.forEach((place: any) => {
      const placeId = place.id || place.tempId || place.miceDemandPlaceId || place.miceSchemePlaceId;
      const savedData = relationData.places.find((item: any) => item.id === placeId);
      if (savedData) {
        place.invoiceTempId = savedData.invoiceTempId;
        place.statementTempId = savedData.statementTempId;
      }
    });
  }

  // 恢复用餐关联数据
  if (schemePlanObj.value?.caterings && relationData.caterings.length > 0) {
    schemePlanObj.value.caterings.forEach((catering: any) => {
      const cateringId =
        catering.id || catering.tempId || catering.miceDemandCateringId || catering.miceSchemeCateringId;
      const savedData = relationData.caterings.find((item: any) => item.id === cateringId);
      if (savedData) {
        catering.invoiceTempId = savedData.invoiceTempId;
        catering.statementTempId = savedData.statementTempId;
      }
    });
  }

  // 恢复用车关联数据
  if (schemePlanObj.value?.vehicles && relationData.vehicles.length > 0) {
    schemePlanObj.value.vehicles.forEach((vehicle: any) => {
      const vehicleId = vehicle.id || vehicle.tempId || vehicle.miceDemandVehicleId || vehicle.miceSchemeVehicleId;
      const savedData = relationData.vehicles.find((item: any) => item.id === vehicleId);
      if (savedData) {
        vehicle.invoiceTempId = savedData.invoiceTempId;
        vehicle.statementTempId = savedData.statementTempId;
      }
    });
  }

  // 恢复服务人员关联数据
  if (schemePlanObj.value?.attendants && relationData.attendants.length > 0) {
    schemePlanObj.value.attendants.forEach((attendant: any) => {
      const attendantId =
        attendant.id || attendant.tempId || attendant.miceDemandAttendantId || attendant.miceSchemeAttendantId;
      const savedData = relationData.attendants.find((item: any) => item.id === attendantId);
      if (savedData) {
        attendant.invoiceTempId = savedData.invoiceTempId;
        attendant.statementTempId = savedData.statementTempId;
      }
    });
  }

  // 恢复活动关联数据
  if (schemePlanObj.value?.activities && relationData.activities.length > 0) {
    schemePlanObj.value.activities.forEach((activity: any) => {
      const activityId =
        activity.id || activity.tempId || activity.miceDemandActivityId || activity.miceSchemeActivityId;
      const savedData = relationData.activities.find((item: any) => item.id === activityId);
      if (savedData) {
        activity.invoiceTempId = savedData.invoiceTempId;
        activity.statementTempId = savedData.statementTempId;
      }
    });
  }

  // 恢复礼品关联数据
  if (schemePresentArr.value && relationData.presents.length > 0) {
    schemePresentArr.value.forEach((present: any) => {
      const presentId = present.id || present.tempId;
      const savedData = relationData.presents.find((item: any) => item.id === presentId);
      if (savedData) {
        present.invoiceTempId = savedData.invoiceTempId;
        present.statementTempId = savedData.statementTempId;

        // 恢复礼品明细关联数据
        if (present.presentDetails && savedData.presentDetails.length > 0) {
          present.presentDetails.forEach((detail: any) => {
            const detailId = detail.id || detail.tempId;
            const savedDetailData = savedData.presentDetails.find((item: any) => item.id === detailId);
            if (savedDetailData) {
              detail.invoiceTempId = savedDetailData.invoiceTempId;
              detail.statementTempId = savedDetailData.statementTempId;
            }
          });
        }
      }
    });
  }

  // 恢复其他方案关联数据
  if (schemeOtherArr.value && relationData.others.length > 0) {
    schemeOtherArr.value.forEach((other: any) => {
      const otherId = other.id || other.tempId;
      const savedData = relationData.others.find((item: any) => item.id === otherId);
      if (savedData) {
        other.invoiceTempId = savedData.invoiceTempId;
        other.statementTempId = savedData.statementTempId;
      }
    });
  }

  // 恢复全单服务费关联数据
  if (schemeFeeObj.value?.serviceFee && relationData.serviceFee) {
    schemeFeeObj.value.serviceFee.invoiceTempId = relationData.serviceFee.invoiceTempId;
    schemeFeeObj.value.serviceFee.statementTempId = relationData.serviceFee.statementTempId;
  }
};

// 价格提报
const biddingSub = async () => {
  // 直接执行校验逻辑，校验通过后再显示确认弹框
  await handleBiddingSub();
};

// 处理提报逻辑
const handleBiddingSub = async () => {

  // todo:hdx 临时注释
  if (schemePlanRef.value && !schemePlanRef.value.SchemePlanSub()) {
    // 日程安排
    return;
  }

  if (schemeMaterialRef.value && !schemeMaterialRef.value.materialSub()) {
    // 布展物料
    return;
  }

  if (schemePresentRef.value && !schemePresentRef.value.presentSub()) {
    // 礼品
    return;
  }

  if (schemeOtherRef.value && !schemeOtherRef.value.otherSub()) {
    // 其他
    return;
  }

  if (schemeFeeRef.value && !schemeFeeRef.value.serviceFeeSub()) {
    // 全单服务费
    return;
  }

  if (schemeFileRef.value && !schemeFileRef.value.serviceFileSub()) {
    // 附件
    return;
  }

  if (supplementEntryRef.value && !supplementEntryRef.value.supplementEntrySub()) {
    return;
  }

  if (billAttachmentRef.value && !billAttachmentRef.value.billAttachmentSub()) {
    // 账单附件
    return;
  }

  // 一手合同验证
  if (hotelContractRef.value && !hotelContractRef.value.hotelContractSub()) {
    return;
  }

  // 发票信息验证
  if (invoiceRef.value && !invoiceRef.value.invoiceSub()) {
    return;
  }

  // 水单信息验证
  if (waterBillRef.value && !waterBillRef.value.waterBillSub()) {
    return;
  }

  // 住宿详单验证
  if (accommodationDetailRef.value && !accommodationDetailRef.value.accommodationDetailSub()) {
    return;
  }

  // 会议现场照片验证
  if (conferencePhotosRef.value && !conferencePhotosRef.value.conferencePhotosSub()) {
    return;
  }

  // // 其他附件验证
  // if (otherAttachmentsRef.value && !otherAttachmentsRef.value.otherAttachmentsSub()) {
  //   return;
  // }

  // 保险附件验证
  if (hasInsuranceData.value && insuranceAttachmentRef.value) {
    if (
      !insuranceAttachmentRef.value.insuranceAttachmentSub ||
      !insuranceAttachmentRef.value.insuranceAttachmentSub()
    ) {
      // 组件内部已经显示了错误消息，这里只需要阻止提交
      return;
    }
  }

  if (BillschemeTotalRef.value && !BillschemeTotalRef.value.Totalamountverification()) {
    return;
  }

  //总额比较 todo:hdx 临时注释
  const totalJudgment = calculateAmount();
  if (totalAmount.value && !totalJudgment) {
    return;
  }

  // 检查是否已导入结算单（放在所有校验的最后） todo:hdx 临时注释
  if (ExportExpenseConfirmationRef.value) {
    // 兼容两种访问方式：.value 和直接访问
    const hasImported =
      ExportExpenseConfirmationRef.value.hasImportedSettlement?.value ||
      ExportExpenseConfirmationRef.value.hasImportedSettlement;

    if (!hasImported) {
      message.warning('请先导入结算单后再提交！');
      return;
    }
  }
  debugger
  const schemeServiceFeeRealTwo = schemeFeeObj.value.serviceFee?.schemeServiceFeeReal || 0;

  const serviceFee = {
    id: schemeFeeObj.value.serviceFee?.id || null,
    schemeServiceFeeReal: schemeServiceFeeRealTwo.toFixed(2),
  };

  const schemeTotalPriceTwo =
    Number(totalPrice.value) + (serviceFee.schemeServiceFeeReal ? Number(serviceFee.schemeServiceFeeReal) : 0) || 0;

  const schemeAllPrice =
    Number(
      totalPrice.value +
        (schemeFeeObj.value.serviceFee && schemeFeeObj.value.serviceFee.schemeServiceFeeReal
          ? schemeFeeObj.value.serviceFee.schemeServiceFeeReal
          : 0),
    ) || 0;

  const otherAttachments = otherAttachmentList.value
      .map((item) => ({
        subType: item.subType, // 子类型，使用附件说明作为子类型
        paths: item.paths, // 附件路径数组
      }))

  // 只把账单上传能改变的值传进去，其他的还是复用接口返回的值
  const params: IBillUploadRequest = {
    ...billDetail.value,
    miceId: miceId.value!,
    miceSchemeId: schemeDetail.value?.id!,
    startDate: schemeDetail.value.startDate!,
    endDate: schemeDetail.value.endDate!,
    billTotalPrice: Number(schemeAllPrice.toFixed(2)), // 账单总金额
    hotels: [...hotelList.value],
    stays: schemePlanObj.value.stays as unknown as IBillUploadStay[],
    caterings: schemePlanObj.value.caterings as unknown as IBillUploadCatering[],
    places: schemePlanObj.value.places as unknown as IBillUploadPlace[],
    vehicles: schemePlanObj.value.vehicles as unknown as IBillUploadVehicle[],
    attendants: schemePlanObj.value.attendants as unknown as IBillUploadAttendant[],
    activities: schemePlanObj.value.activities as unknown as IBillUploadActivity[],
    insurances: schemePlanObj.value.insurances as unknown as IBillUploadInsurance[],
    presents: schemePlanObj.value.presents as unknown as IBillUploadPresent[],
    material: schemeMaterialObj.value?.material as unknown as IBillUploadMaterial,
    traffic: schemePlanObj.value.traffic as unknown as IBillUploadTraffic,
    others: schemeOtherArr.value as unknown as IBillUploadOther[],
    serviceFee: schemeFeeObj.value.serviceFee as unknown as IBillUploadServiceFee,
    additionalItems: additionalItems.value as unknown as IBillUploadAdditionalItem[], // 补充条目
    attachmentContracts: attachmentContracts.value as unknown as IBillUploadAttachmentContract[], // 一手合同附件
    attachmentInvoices: invoiceRef.value ? invoiceRef.value.getInvoiceDataForCache() : [], // 账单附件发票信息（过滤后）
    attachmentStatements: waterBillRef.value ? waterBillRef.value.getWaterBillDataForCache() : [], // 水单
    attachmentStayChecks: accommodationDetailRef.value ? (accommodationDetailRef.value as any).getSubmitData() : [], // 住宿详单
    attachmentPhotos: conferencePhotoList.value
      .map((item) => ({
        subType: item.subType, // 类型
        paths: item.paths, // 附件路径数组
      }))
      .filter((item) => item.subType && item.paths.length > 0), // 过滤掉没有类型或没有文件的项
    attachmentOthers: otherAttachments, // 过滤掉没有说明或没有文件的项
    balances: ExportExpenseConfirmationRef.value ? getSettlementBalances() : [], // 费用确认导出的结算单信息
  };

  subLoading.value = true;

  debugger
  // 处理提交数据中的ID格式
  const processedParams = processSubmitDataIds(params);

  // todo:hdx 临时注释
  const res = await schemeApi.billUploadSubmit({ ...processedParams }, (error) => {
    subLoading.value = false;
    errorModal(error?.message);
  });

  subLoading.value = false;

  if (res && res.success) {
    // // 缓存删除
    // delCache();

    message.success('账单上传成功!');

    // 关闭当前页签
    if(isCloseLastTab) {
      isCloseLastTab.value = true;
    }
    router.push({
      path: '/mice-merchant/billUploadScheme/index',
    });
  }
};

// 导出功能：验证所有数据完整性（复用完成提报的验证逻辑）
const validateAllDataForExport = async (): Promise<boolean> => {
  try {
    // 复用 biddingSub 中的验证逻辑
    if (schemePlanRef.value && !schemePlanRef.value.SchemePlanSub()) {
      return false;
    }

    if (schemeMaterialRef.value && !schemeMaterialRef.value.materialSub()) {
      return false;
    }

    if (schemePresentRef.value && !schemePresentRef.value.presentSub()) {
      return false;
    }

    if (schemeOtherRef.value && !schemeOtherRef.value.otherSub()) {
      return false;
    }

    if (schemeFeeRef.value && !schemeFeeRef.value.serviceFeeSub()) {
      return false;
    }

    if (schemeFileRef.value && !schemeFileRef.value.serviceFileSub()) {
      return false;
    }

    // // 账单上传相关组件验证
    // if (schemeType.value === 'billUpload') {
    //   // if (supplementEntryRef.value && !supplementEntryRef.value.supplementEntrySub()) {
    //   //   return false;
    //   // }

    //   if (billAttachmentRef.value && !billAttachmentRef.value.billAttachmentSub()) {
    //     return false;
    //   }
    // }

    return true;
  } catch (error) {
    console.error('数据验证过程中出现错误:', error);
    return false;
  }
};

// 导出功能：获取导出数据（复用完成提报的数据收集逻辑）
const getExportDataForExport = async () => {
  try {
    // 触发所有组件的数据保存
    schemePlanRef.value && schemePlanRef.value.schemePlanTempSave();
    schemeMaterialRef.value && schemeMaterialRef.value.materialTempSave();
    schemePresentRef.value && schemePresentRef.value.presentTempSave();
    schemeOtherRef.value && schemeOtherRef.value.otherTempSave();
    schemeFeeRef.value && schemeFeeRef.value.serviceFeeTempSave();

    const schemeAllPrice =
      totalPrice.value +
      (schemeFeeObj.value.serviceFee && schemeFeeObj.value.serviceFee.schemeServiceFeeReal
        ? schemeFeeObj.value.serviceFee.schemeServiceFeeReal
        : 0);

    // 构建导出数据（复用 biddingSub 中的数据结构）
    const exportData = {
      miceId: miceId.value,
      sourceId: schemeDetail.value.sourceId,
      mainCode: schemeDetail.value.mainCode,
      miceSchemeId: schemeDetail.value.id,
      billTotalPrice: schemeAllPrice.toFixed(2),
      schemeTotalPrice: schemeAllPrice.toFixed(2),
      remarks: schemeDetail.value.remarks,

      startDate: schemeDetail.value.startDate,
      endDate: schemeDetail.value.endDate,

      hotels: [...hotelList.value],
      ...schemePlanObj.value,
      material: { ...schemeMaterialObj.value?.material },
      traffic: {},
      presents: [...schemePresentArr.value],
      others: [...schemeOtherArr.value],
      ...schemeFeeObj.value,

      // 导出功能不包含账单附件数据
    };

    return exportData;
  } catch (error) {
    console.error('收集导出数据失败:', error);
    return null;
  }
};

const getUser = async () => {
  // 获取登录服务商的类型
  const res = await schemeApi.getMerchantByUser({});

  // 服务商的类型
  // 1-酒店,2-旅行社,3-保险,4-礼品,5-用车
  merchantType.value = res.merchantType;
  merchantId.value = res.id;
};

// 隐藏标的方案 - TODO
const hideBindingSchemeBtn = () => {
  showBindingScheme.value = !showBindingScheme.value;
};

// 流程详情
const getProcessDetails = async (processId = '', verId = '', pdmMerchantPoolId = '') => {
  // 流程ID
  if (!processId) {
    message.error('流程ID不存在！');
    return;
  }

  let res;
  try {
    res = await miceBidManOrderListApi.processDetails({
      id: processId,
      verId: verId,
    });
  } catch (error) {
    console.error('getProcessDetails API 调用失败:', error);
    return;
  }

  // 需求配置
  const demandProcessList = ProcessOrchestrationServiceTypeEnum.getTypeOptions().map((e) => {
    return e.value;
  });
  const demandSets = numComputedArrMethod(res.items, [...demandProcessList]);

  // 需求配置 - 全单服务费是否配置
  showFee.value = demandSets ? demandSets.includes(2048) : false;

  // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
  isCateringStandardControl.value = meetingProcessOrchestration(
    'SCHEME_SUBMIT',
    res.nodes || [],
    'schemeSubmitMealLabelConfigDefine',
  );

  if (!showFee.value) {
    // 未设置全单服务费
    fullServiceRangeRateLimit.value = 0;
  } else {
    // 需求配置 - 全单服务费上限
    const feeConfigList = res.merchantPools || [];
    const feeRange = feeConfigList.filter((e) => e.id == pdmMerchantPoolId) || [];

    // 全单服务费配置项
    serviceFeeSets.value = numComputedArrMethod(feeRange[0].fullServiceRange - 2048, [...demandProcessList]);
    // serviceFeeSets.value = [1, 2, 4, 16];
    fullServiceRangeRateLimit.value = feeRange[0].fullServiceRangeRateLimit;
    fullServiceRemark.value = feeRange[0].fullServiceRemark;
  }
};

const delCacheBtn = async () => {
  await delCache();
  window.location.reload();
};

onMounted(async () => {
  const record = resolveParam(route.query.record as string);
  miceId.value = record.miceId;
  miceSchemeId.value = record.miceSchemeId || null;
  schemeType.value = record.schemeType;
  hotelLockId.value = record.hotelLockId;
  miceSchemeDemandHotelLockId.value = record.miceSchemeDemandHotelLockId;
  pdMainId.value = record.pdMainId;
  pdVerId.value = record.pdVerId;

  await getUser();

  isShowDel.value = localStorage.getItem('testProcessSignForCiCi') === '1';
  debugger
  // 先查询缓存，如果有缓存，先读缓存，没有缓存就读详情
  cacheLoading.value = true;
  const cacheData = await getDetaileFromCache();
  if (cacheData) {
    billDetail.value = cacheData;
  } else {
    await getBillDetails();
  }

  // 获取最终执行方案
  await getSchemeDetails();

  // 1min自动保存
  countDownOneMin();

  // 获取签到数据
  if (billDetail.value?.mainCode) {
    await fetchSignInPersonList(billDetail.value.mainCode);
  }

  cacheLoading.value = false;

  // 初始化账单附件数据（仅在没有缓存数据时）
  if (!isSchemeCache.value) {
    // 初始化发票和水单列表
    invoiceList.value = [];
    waterBillList.value = [];

    // 初始化新增组件数据
    accommodationDetailList.value = [];
    conferencePhotoList.value = [];
    otherAttachmentList.value = [];

    // 酒店列表
    billHotelList.value = [];
    // 初始化一手合同附件数据
    attachmentContracts.value = [];
    additionalItems.value = [];
  }
});

onBeforeUnmount(() => {
  clearInterval(autoSave.value);
  clearInterval(countdownTimer.value);
});
</script>

<template>
  <!-- 方案互动 -->
  <div class="scheme_interact" ref="schemeContainerRef">
    <a-spin :spinning="cacheLoading || spinLoading || schemeLoading || subLoading" tip="Loading..." size="large">
      <!-- 顶部 -->
      <billUploadschemeInfo class="interact_header" :demandInfo="schemeDetail" />

      <div class="interact_content mt20">
        <!-- 标题 -->
        <a-affix :offset-top="0" :target="() => schemeContainerRef">
          <div class="interact_demand_title mb12 pb12">
            <div class="plan_title" v-show="showBindingScheme">
              <img src="@/assets/image/common/demand_icon.png" width="16" />
              <span class="ml12"> 最终执行方案 </span>
            </div>
            <div class="plan_title">
              <img src="@/assets/image/common/plan_icon.png" width="16" />
              <span class="ml12"> 我的账单 </span>
            </div>
          </div>
        </a-affix>

        <!-- 实际总签到人数 -->
        <div class="total_checkin_info mb16">
          <div class="checkin_content">
            <span class="checkin_label">实际总签到人数：</span>
            <span class="checkin_number">
              <a-spin :spinning="signInLoading" size="small"> {{ realSignInPersonNum }}人 </a-spin>
            </span>
            <a-button type="link" size="small" class="view_btn" @click="viewAccommodationDetail"> 查看 </a-button>
          </div>
        </div>
        <a-alert
          v-if="shouldShowAbandonReason"
          class="mb16 demand_reject_reason"
          message="驳回原因："
          :description="schemeAbandonReason"
          show-icon
          type="warning"
        />
        <!-- 酒店需求 -->
        <div class="interact_hotel common_content p24 mb16" v-if="merchantType === 1 || merchantType === 2">
          <schemeHotel
            :showBindingScheme="showBindingScheme"
            :hotels="schemeDetail.hotels"
            :schemeHotels="billDetail.hotels"
            :schemeType="schemeType"
            :merchantType="merchantType"
            @hotelsEmit="hotelsEmit"
          />
        </div>
        <!-- 日程安排 -->
        <div class="interact_schedule_plan common_content p24 mb16">
          <schemePlan
            ref="schemePlanRef"
            v-if="merchantType !== 4"
            :showBindingScheme="showBindingScheme"
            :schemeContainerRef="schemeContainerRef"
            :processNode="processNode"
            :schemeType="schemeType"
            :schemeDetail="schemeDetail"
            :billDetail="billDetail"
            :isSchemeCache="isSchemeCache"
            :hotelList="hotelList"
            :merchantType="merchantType"
            :isCateringStandardControl="isCateringStandardControl"
            @planPriceEmit="planPriceEmit"
            @planEachPriceEmit="planEachPriceEmit"
            @schemePlanEmit="schemePlanEmit"
          />
        </div>
        <!-- 保单附件 -->
        <div
          v-if="hasInsuranceData && merchantType === 3"
          class="interact_insurance_attachment common_content p24 mb16"
        >
          <bill-uploadscheme-insurance-attachment
            ref="insuranceAttachmentRef"
            :schemeType="schemeType"
            :schemeItem="schemeDetail"
            :schemeIndex="0"
            :attachmentList="insuranceAttachmentList"
            :isSchemeCache="isSchemeCache"
            :schemeCacheItem="billDetail"
            @attachmentEmit="handleInsuranceAttachmentEmit"
            @attachmentTempIdEmit="handleInsuranceAttachmentTempId"
          />
        </div>
        <!-- 布展物料 -->
        <div
          v-if="
            schemeDetail.material &&
            schemeDetail.material.materialDetails &&
            schemeDetail.material.materialDetails.length > 0 &&
            (merchantType === MerchantType.HOTEL.code || merchantType === MerchantType.TRAVEL.code)
          "
          class="interact_wu common_content p24 mb16"
        >
          <scheme-material
            ref="schemeMaterialRef"
            :showBindingScheme="showBindingScheme"
            :schemeType="schemeType"
            :demandInfo="schemeDetail"
            :schemeCacheInfo="billDetail"
            :isSchemeCache="isSchemeCache"
            @materialPriceEmit="materialPriceEmit"
            @schemeMaterialEmit="schemeMaterialEmit"
          />
        </div>
        <!-- 礼品 -->
        <div
          v-if="schemeDetail.presents && schemeDetail.presents.length > 0 && merchantType === 4"
          class="interact_gift common_content p24 mb16"
        >
          <scheme-presents
            ref="schemePresentRef"
            :showBindingScheme="showBindingScheme"
            :schemeType="schemeType"
            :demandInfo="schemeDetail"
            :schemeCacheInfo="billDetail"
            :isSchemeCache="isSchemeCache"
            @presentPriceEmit="presentPriceEmit"
            @schemePresentEmit="schemePresentEmit"
          />
        </div>
        <!-- 其他 -->
        <div
          v-if="schemeDetail.others && schemeDetail.others.length > 0 && (merchantType === 1 || merchantType === 2)"
          class="interact_other common_content p24 mb16"
        >
          <scheme-other
            ref="schemeOtherRef"
            :showBindingScheme="showBindingScheme"
            :schemeType="schemeType"
            :demandInfo="schemeDetail"
            :schemeCacheInfo="billDetail"
            :isSchemeCache="isSchemeCache"
            @otherPriceEmit="otherPriceEmit"
            @schemeOtherEmit="schemeOtherEmit"
          />
        </div>
        <!-- 全单服务费方案 -->
        <div
          class="interact_service_fee common_content p24 mb16"
          v-if="(merchantType === 1 || merchantType === 2) && showFee"
        >
          <scheme-service-fee
            ref="schemeFeeRef"
            :schemeType="schemeType"
            :demandInfo="schemeDetail"
            :isSchemeCache="isSchemeCache"
            :materialPrice="materialPrice"
            :presentPrice="presentPrice"
            :otherPrice="otherPrice"
            :planEachPriceList="planEachPriceList"
            :fullServiceRangeRateLimit="fullServiceRangeRateLimit"
            :fullServiceRemark="fullServiceRemark"
            :serviceFeeSets="serviceFeeSets"
            :cacheServiceFee="billDetail.serviceFee"
            :schemeCacheInfo="billDetail"
            :invoiceTempId="invoiceTempId"
            :statementTempId="statementTempId"
            @schemeFeeEmit="schemeFeeEmit"
            :schemeFee="schemeFeeObj.serviceFee"
          />
        </div>
        <!-- 合计 -->
        <div class="interact_total_table common_content p24">
          <scheme-total
            ref="BillschemeTotalRef"
            :schemeCacheInfo="schemeTotalInfo"
            :totalPrice="totalPrice"
            :leftPlanTotalPrice="leftPlanTotalPrice"
            :additionalItems="additionalItems"
            @hanleTotalAmount="hanleTotalAmount"
          />
        </div>

        <!-- 账单上传相关功能 -->
        <div v-if="schemeType === 'billUpload'" class="bill-upload-sections" style="margin-top: 20px">
          <!-- 补充条目 -->
          <div
            class="interact_supplement_entry common_content p24 mb16"
            v-if="merchantType === 1 || merchantType === 2"
          >
            <billUploadschemeSupplementEntry
              ref="supplementEntryRef"
              :miceId="miceId"
              :schemeDetail="billDetail"
              @supplementEntryEmit="supplementEntryEmit"
              :invoiceTempId="invoiceTempId"
              :statementTempId="statementTempId"
              :additionalItems="additionalItems"
            />
          </div>

          <!-- 费用确认导出 -->
          <div class="interact_export_confirmation common_content mb16">
            <ExportExpenseConfirmation
              ref="ExportExpenseConfirmationRef"
              :validateAllData="validateAllDataForExport"
              :getExportData="getExportDataForExport"
              :schemeType="schemeType"
            />
          </div>

          <!-- 一手合同 -->
          <div class="interact_hotel_contract common_content mb16" v-if="merchantType === 1 || merchantType === 2">
            <billUploadschemeHotelContract
              ref="hotelContractRef"
              :hotelList="billHotelList"
              :contractData="attachmentContracts"
              @hotelContractEmit="handleHotelContractEmit"
            />
          </div>

          <!-- 发票信息 -->
          <div class="interact_invoice common_content mb16" v-if="merchantType === 1 || merchantType === 2">
            <billUploadschemeInvoice
              ref="invoiceRef"
              :invoiceList="invoiceList"
              @invoiceEmit="handleInvoiceEmit"
              @viewRelatedBill="handleViewRelatedBill"
              @invoiceDeleted="handleInvoiceDeleted"
            />
          </div>

          <!-- 水单信息 -->
          <div class="interact_water_bill common_content mb16" v-if="merchantType === 1 || merchantType === 2">
            <billUploadschemeWaterBill
              ref="waterBillRef"
              :waterBillList="waterBillList"
              @waterBillEmit="handleWaterBillEmit"
              @viewRelatedBill="handleViewRelatedBill"
              @waterBillDeleted="handleWaterBillDeleted"
            />
          </div>

          <!-- 住宿详单 -->
          <div
            class="interact_accommodation_detail common_content p24 mb16"
            v-if="merchantType === 1 || merchantType === 2"
          >
            <billUploadschemeAccommodationDetail
              ref="accommodationDetailRef"
              :accommodationDetailList="accommodationDetailList"
              :realSignInPersonNum="realSignInPersonNum"
              @accommodationDetailEmit="handleAccommodationDetailEmit"
            />
          </div>

          <!-- 会议现场照片 -->
          <div
            class="interact_conference_photos common_content p24 mb16"
            v-if="merchantType === 1 || merchantType === 2"
          >
            <billUploadschemeConferencePhotos
              ref="conferencePhotosRef"
              :conferencePhotoList="conferencePhotoList"
              @conferencePhotosEmit="handleConferencePhotosEmit"
            />
          </div>

          <!-- 其他附件 -->
          <div
            class="interact_other_attachments common_content p24 mb16"
            v-if="merchantType === 1 || merchantType === 2"
          >
            <billUploadschemeOtherAttachments
              ref="otherAttachmentsRef"
              :schemeDetail="billDetail"
              :otherAttachmentList="otherAttachmentList"
              @otherAttachmentsEmit="handleOtherAttachmentsEmit"
              @otherAttachmentDeleted="handleOtherAttachmentDeleted"
            />
          </div>
        </div>

        <slot name="billUpload"></slot>
      </div>

      <!-- 关联账单弹框 -->
      <RelatedBillDialog
        v-model:visible="relatedBillVisible"
        :bill-type="currentBillType"
        :bill-data="currentBillData"
        :demandInfo="{
          ...schemePlanObj,
          presents: schemePresentArr,
          others: schemeOtherArr,
          serviceFee: schemeFeeObj?.serviceFee,
          additionalItems: additionalItems, // 🔧 新增：补充条目数据
          schemeMaterial: schemeMaterialObj?.material, // 🔧 新增：布展物料数据
        }"
        :existing-related-bills="currentBillData?.relatedBills || []"
        :excluded-bill-ids="allRelatedBillIds"
        @confirm="handleRelatedBillConfirm"
        @updateStaysInvoiceId="handleUpdateStaysInvoiceId"
        @updateRelatedAmount="handleUpdateRelatedAmount"
        @updateAttachmentAmount="handleUpdateAttachmentAmount"
      />

      <!-- 签到人数明细弹框 -->
      <a-modal v-model:open="checkInDetailVisible" title="实际签到人数明细" width="1000px" :footer="null">
        <div class="checkin-detail-content">
          <!-- 系统签到数据 -->
          <a-spin :spinning="signInLoading">
            <a-table
              v-if="signInPersonList && signInPersonList.length > 0"
              :dataSource="signInPersonList"
              :columns="signInPersonColumns"
              :pagination="false"
              size="small"
              :scroll="{ y: 400 }"
            >
              <template #summary>
                <a-table-summary-row>
                  <a-table-summary-cell :col-span="3" class="summary-label">总计：</a-table-summary-cell>
                  <a-table-summary-cell class="summary-value">{{ signInTotalCount }}人</a-table-summary-cell>
                </a-table-summary-row>
              </template>
            </a-table>
            <div v-else class="no-data">
              <a-empty description="暂无系统签到数据" />
            </div>
          </a-spin>
        </div>
      </a-modal>
      <!-- 操作 -->
      <a-affix :offset-bottom="0">
        <div class="btns_mar"></div>
        <div class="interact_btns">
          <div class="flex_between">
            <div class="sub_auto_save mr24">
              <div v-show="countdownTime === 0" class="auto_save_img"></div>
              <div class="auto_save_time pl5">
                {{ countdownTime === 0 ? '已自动保存' : countdownTime + 's后自动保存' }}
              </div>
            </div>

            <div class="sub_btns">
              <a-button class="mr8" @click="clearAllData">清除</a-button>
              <a-button class="mr8" @click="schemeTemporarily('hand')">暂存</a-button>
              <a-button type="primary" :loading="subLoading" @click="biddingSub()">完成提报</a-button>
            </div>
          </div>
        </div>
      </a-affix>
    </a-spin>
  </div>
</template>

<style>
@import './schemeComponent/billUploadschemeInteract.scss';
</style>
<style scoped lang="less">
.scheme_interact {
  height: 100%;
  overflow-y: auto;

  .demand_reject_reason {
    padding: 24px;
    border-radius: 12px;
  }

  .interact_header {
  }

  .interact_content {
  }

  .interact_demand_title {
    display: flex;
    justify-content: space-between;
    background: #f5f5f5;

    .plan_title {
      display: flex;
      justify-content: center;
      align-items: center;

      width: calc(50% - 6px);
      height: 32px;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 14px;
      color: #1d2129;
      background: #d5e6ff;
      border-radius: 4px;
      border: 1px solid rgba(24, 104, 219, 0.3);
    }
  }

  .interact_hotel {
  }

  .interact_schedule_plan {
  }

  .interact_wu {
  }

  .interact_gift {
  }

  .interact_other {
  }

  .interact_service_fee {
  }

  .interact_total_table {
  }

  // 账单附件相关样式
  .interact_bill_attachment_title {
    .interact_title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      font-size: 18px;
      font-weight: 500;

      .interact_shu {
        width: 4px;
        height: 20px;
        background: #1868db;
        border-radius: 2px;
      }

      span {
        font-size: 18px;
        font-weight: 500;
        color: #1d2129;
      }
    }
  }

  .interact_hotel_contract {
  }

  .interact_invoice {
  }

  .interact_water_bill {
  }

  .interact_accommodation_detail {
  }

  .interact_conference_photos {
  }

  .interact_other_attachments {
  }

  .interact_insurance_attachment {
  }

  .btns_mar {
    height: 16px;
    background: #f5f5f5;
  }

  .total_checkin_info {
    .checkin_content {
      height: 40px;
      padding: 0 24px;
      background: #f7f8fa;
      border: 1px solid #e5e6eb;
      border-radius: 4px;
      display: flex;
      align-items: center;

      .checkin_label {
        font-size: 14px;
        color: #4e5969;
        margin-right: 8px;
      }

      .checkin_number {
        font-size: 14px;
        font-weight: 500;
        color: #1868db;
        margin-right: 12px;
      }

      .view_btn {
        padding: 0;
        height: auto;
        font-size: 14px;
        color: #1868db;

        &:hover {
          color: #0e4ba1;
        }
      }
    }
  }

  .interact_btns {
    width: 100%;

    height: 56px !important;
    line-height: 56px;
    padding: 0 24px;
    background: #ffffff;
    box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
    filter: blur(0px);
    border-top: 1px solid #e8e8e8;

    .flex_between {
      display: flex;
      justify-content: right;
      align-items: center;

      .sub_auto_save {
        display: flex;

        color: #4e5969;
        line-height: 20px;

        .auto_save_img {
          width: 18px;
          height: 18px;
          background: url('@/assets/image/demand/right_green.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }

        .auto_save_time {
          text-align: right;
        }
      }

      .sub_btns {
      }
    }
  }

  // 签到人数明细弹框样式
  :deep(.checkin-detail-content) {
    .ant-table-summary-row {
      .summary-label {
        font-weight: 500;
        text-align: right !important;
      }

      .summary-value {
        color: #1868db;
        font-weight: 600;
        font-size: 16px;
      }
    }

    .no-data {
      padding: 40px 0;
      text-align: center;
    }
  }
}
</style>
