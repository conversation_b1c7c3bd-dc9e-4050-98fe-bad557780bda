<!-- 通用查看详情组件 -->
<script setup lang="ts">
import {
  message,
  <PERSON>,
  But<PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bs as ATabs,
  TabPane as ATabPane,
  Table as hTable,
  Upload as hUpload,
  Modal
} from 'ant-design-vue';
import { paymentFromApi, fileApi } from '@haierbusiness-front/apis';
import { ref, watch, computed, inject } from 'vue';
import type { Ref } from 'vue';
import { PaymentFromStatusMap, InvoiceStatusMap, FileTypeEnum } from '@haierbusiness-front/common-libs';
import { useRouter } from 'vue-router';
import { getFileNameFromPath } from '@haierbusiness-front/utils';

// 组件属性定义
interface Props {
  type: 'paymentBill' | 'paymentOrder' | 'platformServiceFeePayment' | 'payment';
  record?: any;
  mode?: 'view' | 'edit'; // 新增模式参数：view-查看模式，edit-编辑模式
}

const props = withDefaults(defineProps<Props>(), {
  type: 'paymentBill',
  record: null,
  mode: 'edit'
});

const router = useRouter();

// 获取关闭标签页控制器
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');

// 响应式数据
const detailLoading = ref(false);
const currentDetailRecord = ref<any>(null);
const activeKey = ref('1');

// 上传相关
const uploadLoading = ref(false);
const fileList = ref<any[]>([]);
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// API映射配置
const apiMap = {
  paymentBill: paymentFromApi.getPaymentDetails,
  paymentOrder: paymentFromApi.getMerchantDetails,
  platformServiceFeePayment: paymentFromApi.getMerchantPaymentDetails,
  payment: paymentFromApi.getMerchantDetails // payment类型使用同样的API
};

// 字段映射配置
const fieldMap = {
  paymentBill: {
    code: 'receivePaymentCode',
    codeLabel: '收款单号',
    amount: 'receivePaymentAmount',
    merchantName: 'merchantName'
  },
  paymentOrder: {
    code: 'paymentCode',
    codeLabel: '付款单号',
    amount: 'paymentAmount',
    merchantName: 'merchantName'
  },
  platformFee: {
    code: 'receivePaymentCode',
    codeLabel: '缴费单号',
    amount: 'receivePaymentAmount',
    merchantName: 'merchantName'
  },
  platformServiceFeePayment: {
    code: 'receivePaymentCode',
    codeLabel: '缴费单号',
    amount: 'receivePaymentAmount',
    merchantName: 'merchantName'
  },
  payment: {
    code: 'paymentCode',
    codeLabel: '付款单号',
    amount: 'paymentAmount',
    merchantName: 'merchantName'
  }
};

// 获取详情数据
const fetchDetailData = async (record: any) => {
  if (!record?.id) {
    console.error('记录ID不存在');
    return;
  }

  detailLoading.value = true;

  try {
    const apiFunction = apiMap[props.type];
    const result = await apiFunction(record.id);
    currentDetailRecord.value = result;
  } catch (error) {
    console.error('获取详情失败:', error);
    message.error('获取详情失败，请重试');
  } finally {
    detailLoading.value = false;
  }
};

// 监听record变化
watch(
  () => props.record,
  (newRecord) => {
    if (newRecord) {
      fetchDetailData(newRecord);
    }
  },
  { immediate: true }
);

// 页面模式下的返回功能
const goBack = () => {
  // 设置关闭当前标签页标识
  if (isCloseLastTab) {
    isCloseLastTab.value = true;
  }
  router.back();
};

// 获取当前字段配置
const currentFieldConfig = computed(() => fieldMap[props.type]);

// 格式化金额显示
const formatAmount = (amount: number | string) => {
  if (!amount) {
    return '0.00';
  }
  return Number(amount).toFixed(2);
};

// 获取状态文本
const getStatusText = (status: any) => {
  if (props.type === 'platformServiceFeePayment') {
    return PaymentFromStatusMap[status as keyof typeof PaymentFromStatusMap] || '未知状态';
  } else {
    return InvoiceStatusMap[status as keyof typeof InvoiceStatusMap] || '未知状态';
  }
};

// 获取驳回原因文本
const getRejectReasonText = (data: any) => {
  if (!data) return '暂无驳回原因';
  
  // 收款驳回状态(30)优先使用remark字段
  if (data.status === 30) {
    return data.remark || data.rejectReason || '暂无驳回原因';
  }
  // 其他驳回状态优先使用rejectReason字段，remark作为备用
  return data.rejectReason || data.remark || '暂无驳回原因';
};

// 详情页订单表格列
const detailOrderColumns = computed(() => [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center'
  },
  {
    title: '会议时间',
    width: '200px',
    align: 'center',
    customRender: ({ record }: any) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    }
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center'
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }: any) => (text != null ? `${text.toFixed(2)}元` : '')
  },
  {
    title: props.type === 'platformServiceFeePayment' ? '服务费率' : '付款比例',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }: any) => (text != null ? `${text.toFixed(2)}%` : '')
  },
  {
    title: props.type === 'platformServiceFeePayment' ? '收款金额' : '付款金额',
    dataIndex: 'paymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }: any) => (text != null ? `${text.toFixed(2)}元` : '')
  }
]);

// 详情页发票表格列
const detailInvoiceColumns: any[] = [
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    align: 'center'
  },
  {
    title: '发票日期',
    dataIndex: 'invoiceDate',
    align: 'center'
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    align: 'center',
    customRender: ({ text }: any) => (text != null ? `${text.toFixed(2)}元` : '')
  }
];

// 计算发票金额合计
const calculateInvoiceTotal = () => {
  if (!currentDetailRecord.value) {
    return '0.00';
  }
  const invoiceData = getInvoiceData();
  const total = invoiceData.reduce((sum: number, item: any) => sum + (item.invoiceAmount || 0), 0);
  return `${total.toFixed(2)}元`;
};

// 获取发票数据
const getInvoiceData = () => {
  if (!currentDetailRecord.value || !currentDetailRecord.value.miceInvoiceQueryDetails) {
    return [];
  }

  return currentDetailRecord.value.miceInvoiceQueryDetails.map((invoice: any, index: number) => ({
    key: invoice.id || index,
    invoiceNumber: invoice.invoiceNumber || '',
    invoiceDate: invoice.invoiceDate || '',
    invoiceAmount: invoice.invoiceAmount || 0
  }));
};

// 获取订单数据
const getOrderData = () => {
  if (!currentDetailRecord.value) {
    return [];
  }

  // 根据不同类型使用不同的数据源
  let recordsData = [];
  if (props.type === 'platformServiceFeePayment') {
    // 平台服务费缴纳使用 receivePaymentRecordsDetails
    recordsData = currentDetailRecord.value.receivePaymentRecordsDetails || [];
  } else {
    // 其他类型使用 paymentRecordsDetails
    recordsData = currentDetailRecord.value.paymentRecordsDetails || [];
  }

  return recordsData.map((record: any, index: number) => ({
    key: record.id || index,
    mainCode: record.mainCode || '',
    startDate: record.startDate || '',
    endDate: record.endDate || '',
    operatorName: record.operatorName || '',
    billAmount: record.billAmount || 0,
    feeRate: record.feeRate || 0,
    paymentAmount:
      props.type === 'platformServiceFeePayment' ? record.receivePaymentAmount || 0 : record.paymentAmount || 0
  }));
};

// 文件上传处理
const uploadRequest = (options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path;
      options.file.fileName = options.file.name;
      options.onProgress(100);
      options.onSuccess(it, options.file);

      // 确保文件被添加到列表中
      if (!fileList.value.some((f) => f.fileName === options.file.name)) {
        fileList.value.push(options.file);
      }
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error('文件上传失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 文件删除处理
const handleFileRemove = (file: any) => {
  const index = fileList.value.findIndex((f) => f.uid === file.uid);
  if (index > -1) {
    fileList.value.splice(index, 1);
  }
};

// 提交上传的付款凭证
const submitUpload = () => {
  if (fileList.value.length === 0) {
    message.error('请先上传付款凭证');
    return;
  }

  if (!currentDetailRecord.value?.id) {
    message.error('记录ID不存在');
    return;
  }

  // 提取文件路径
  const attachmentFiles = fileList.value.map((file) => file.filePath).filter(Boolean) as string[];

  if (attachmentFiles.length === 0) {
    message.error('文件上传未完成，请重试');
    return;
  }

  uploadLoading.value = true;

  // 调用上传接口
  paymentFromApi
    .uploadPaymentAttachment({
      id: currentDetailRecord.value.id,
      attachmentFile: attachmentFiles
    })
    .then(() => {
      message.success('付款凭证上传成功');
      fileList.value = [];
      
      // 设置关闭当前标签页标识
      if (isCloseLastTab) {
        isCloseLastTab.value = true;
      }
      
      router.push({
        path: '/mice-merchant/paymentDocument/paymentOrderList'
        
      });
    })
    .catch((error) => {
      console.error('提交付款凭证失败:', error);
      message.error('提交付款凭证失败，请重试');
    })
    .finally(() => {
      uploadLoading.value = false;
    });
};

// 判断是否可以上传凭证
const canUploadVoucher = computed(() => {
  // 查看模式下不允许上传凭证
  if (props.mode === 'view') return false;

  if (!currentDetailRecord.value) return false;
  const status = currentDetailRecord.value.status;
  // 待上传支付凭证 或 收款驳回状态可以上传
  return status === 10 || status === 30;
});
</script>

<template>
  <div class="page-container">
    <Spin :spinning="detailLoading">
      <div v-if="currentDetailRecord" class="detail-card">
        <div class="payment-info-card">
          <div class="payment-info-content">
            <!-- 主标题：服务商名称 -->
            <div class="payment-main-title">
              <div class="payment-main-icon"></div>
              <span class="payment-main-name">{{
                currentDetailRecord[currentFieldConfig.merchantName] || '未知服务商'
              }}</span>
            </div>

            <!-- 付款单号 -->
            <div class="payment-code">
              <span>{{ currentFieldConfig.codeLabel }}：{{ currentDetailRecord[currentFieldConfig.code] || '-' }}</span>
            </div>

            <!-- 详细信息 -->
            <div class="payment-info-details">
              <div class="payment-info-item">
                <span class="info-title amount-icon"
                  >{{ props.type === 'paymentOrder' ? '付款总金额' : '缴费总金额' }}：</span
                >
                <span class="info-value">{{ formatAmount(currentDetailRecord[currentFieldConfig.amount]) }}元</span>
              </div>

              <!-- 创建时间 -->
              <div class="payment-info-item">
                <span class="info-title status-icon">创建时间：</span>
                <span class="info-value">{{ currentDetailRecord.gmtCreate || '-' }}</span>
              </div>

              <!-- 状态信息 -->
              <div v-if="currentDetailRecord.status" class="payment-info-item">
                <span class="info-title status-icon">状态：</span>
                <span class="info-value">
                  {{ getStatusText(currentDetailRecord.status) }}
                </span>
              </div>

              <!-- 驳回原因显示（当状态为驳回时） -->
              <div
                v-if="currentDetailRecord.status === 30 || currentDetailRecord.status === 50"
                class="payment-info-item"
              >
                <span class="info-title remark-icon">驳回原因：</span>
                <span class="info-value">{{ getRejectReasonText(currentDetailRecord) }}</span>
              </div>
            </div>

            <!-- 附件区域：查看模式下不显示付款凭证 -->
            <div
              v-if="currentDetailRecord.attachmentFiles && (currentDetailRecord.attachmentFiles.some((f: any) => f.type === FileTypeEnum.PAYMENT_VOUCHER_SERVICE_PROVIDER) || currentDetailRecord.attachmentFiles.some((f: any) => f.type === FileTypeEnum.RECEIPT_VOUCHER_FINANCE))"
              class="attachment-section"
            >
              <div class="attachment-row">
                <!-- 付款凭证：查看模式下不显示 -->
                <div v-if="props.mode !== 'view' && currentDetailRecord.attachmentFiles.some((f: any) => f.type === FileTypeEnum.PAYMENT_VOUCHER_SERVICE_PROVIDER)" class="attachment-item">
                  <span class="info-title file-icon">付款凭证：</span>
                  <div class="attachment-files">
                    <template v-for="(file, index) in currentDetailRecord.attachmentFiles.filter((f: any) => f.type === FileTypeEnum.PAYMENT_VOUCHER_SERVICE_PROVIDER)" :key="'payment-'+index">
                      <a :href="file.filePath || file.path" class="file-link" target="_blank">
                        {{ getFileNameFromPath(file.filePath || file.path || file.fileName || `付款凭证${index + 1}`) }}
                      </a>
                    </template>
                  </div>
                </div>

                <!-- 收款凭证 -->
                <div v-if="currentDetailRecord.attachmentFiles.some((f: any) => f.type === FileTypeEnum.RECEIPT_VOUCHER_FINANCE)" class="attachment-item">
                  <span class="info-title file-icon">收款凭证：</span>
                  <div class="attachment-files">
                    <template v-for="(file, index) in currentDetailRecord.attachmentFiles.filter((f: any) => f.type === FileTypeEnum.RECEIPT_VOUCHER_FINANCE)" :key="'receipt-'+index">
                      <a :href="file.filePath || file.path" class="file-link" target="_blank">
                        {{ getFileNameFromPath(file.filePath || file.path || file.fileName || `收款凭证${index + 1}`) }}
                      </a>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tab页 -->
        <a-tabs v-model:active-key="activeKey" class="detail-tabs">
          <a-tab-pane key="1" tab="订单">
            <div class="tab-content">
              <div class="table-container">
                <h-table
                  :columns="detailOrderColumns"
                  :data-source="getOrderData()"
                  :pagination="false"
                  :scroll="{ x: 'max-content', y: 'calc(100vh - 270px)' }"
                  bordered
                  size="small"
                />
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="发票">
            <div class="tab-content">
              <div class="invoice-total"><strong>发票金额合计：</strong>{{ calculateInvoiceTotal() }}</div>
              <div class="table-container">
                <h-table
                  :columns="detailInvoiceColumns"
                  :data-source="getInvoiceData()"
                  :pagination="false"
                  :scroll="{ x: 'max-content', y: 'calc(100vh - 270px)' }"
                  bordered
                  size="small"
                />
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>

        <!-- 上传操作区域（仅在特定状态下显示） -->
        <div v-if="canUploadVoucher" class="upload-section">
          <!-- 文件上传 -->
          <div class="upload-item">
            <div class="inline-label">付款凭证：</div>
            <div class="inline-content">
              <h-upload
                v-model:fileList="fileList"
                :custom-request="uploadRequest"
                :maxCount="5"
                :multiple="true"
                :show-upload-list="true"
                accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
                @remove="handleFileRemove"
              >
                <HButton :loading="uploadLoading"> 上传文件 </HButton>
              </h-upload>
              <div class="upload-tip">支持格式：PDF、Word、Excel、图片，最多上传5个文件</div>
            </div>
          </div>
        </div>
      </div>
    </Spin>

    <!-- 底部固定按钮 -->
    <div class="footer-container">
      <HButton @click="goBack">返回</HButton>
      <template v-if="canUploadVoucher">
        <HButton :loading="uploadLoading" @click="submitUpload" type="primary">提交凭证</HButton>
      </template>
    </div>
  </div>
</template>

<style lang="less" scoped>
.page-container {
  background-color: #f5f5f5;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .back-button {
    margin-right: 16px;
    display: flex;
    align-items: center;
    padding: 4px 8px;
  }

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
  }
}

.detail-card {
  background: white;
  min-height:calc(100vh - 113px);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .payment-info-content {
    padding: 12px 16px;
    width: 100%;
    height: 100%;
    background: url('@/assets/image/scheme/mice_bgc.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 6px;
  }

  .payment-main-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  .payment-main-icon {
    width: 28px;
    height: 28px;
    background-image: url('@/assets/image/scheme/mice_name.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-right: 12px;
  }

  .payment-main-name {
    font-family: PingFangSCSemibold, PingFangSCSemibold;
    font-weight: normal;
    font-size: 20px;
    color: #1d2129;
    line-height: 28px;
  }

  .payment-code {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;
  }

  .payment-info-details {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .payment-info-item {
    flex: 1;
    min-width: 0;
    font-size: 14px;
    color: #86909c;
    line-height: 15px;
  }

  .attachment-section {
    margin-top: 16px;
    font-size: 14px;
    color: #86909c;

    .attachment-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .attachment-item {
      width: 24%;
      min-width: 0;
      font-size: 14px;
      color: #86909c;
      line-height: 15px;
      display: flex;
      align-items: flex-start;

      .info-title {
        flex-shrink: 0;
      }

      .attachment-files {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-left: 8px;
        flex: 1;
        min-width: 0;
      }
    }
  }

  .info-title {
    display: inline-block;
    text-indent: 26px;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center left;
    margin-right: 8px;

    &.amount-icon {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    &.file-icon {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    &.status-icon {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }

    &.remark-icon {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }
  }

  .info-value {
    color: #1d2129;
  }

  .file-link {
    margin-right: 16px;
    color: #1890ff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .detail-tabs {
    padding: 0 20px;
    .tab-content {
      min-height: 200px;

      .table-container {
        overflow: hidden;
        border: 1px solid #f0f0f0;
        border-radius: 6px;

        :deep(.ant-table-wrapper) {
          height: 100%;

          .ant-table {
            height: 100%;

            .ant-table-container {
              height: 100%;

              .ant-table-tbody > tr > td {
                padding: 6px;
                height: 1px;
              }

              .ant-table-thead > tr > th {
                padding: 8px 16px;
              }

              .ant-table-body {
                height: calc(100% - 55px);
                overflow-y: auto !important;
              }
            }
          }
        }
      }
    }

    .invoice-total {
      margin-bottom: 16px;
      padding: 12px 16px;
      background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%);
      border-radius: 6px;
      border-left: 4px solid #1890ff;

      strong {
        color: #1890ff;
        font-size: 14px;
      }
    }
  }

  .upload-section {
    padding: 20px;

    .upload-item {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .inline-label {
        flex-shrink: 0;
        width: 80px;
        font-weight: 500;
        color: #262626;
        padding-top: 6px;
      }

      .inline-content {
        flex: 1;
      }

      .upload-tip {
        margin-top: 8px;
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }

  .permission-tip {
    margin-bottom: 24px;

    .tip-content {
      text-align: center;
      color: #8c8c8c;
      padding: 24px;
    }
  }

  .footer-actions {
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;

    .action-button {
      margin-left: 12px;
    }
  }
}


// 底部固定按钮
.footer-container {
  position: fixed;
  bottom: 0;
  left: 230px; // 减去左侧菜单宽度
  right: 10px;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 10px 24px;
  display: flex;
  justify-content: flex-end; // 靠右对齐
  gap: 12px; // 按钮间距
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

// 响应式设计
@media (max-width: 768px) {
  .payment-info-content {
    padding: 16px 20px;
  }

  .payment-info-details {
    flex-direction: column;
    gap: 12px;
  }

  .payment-info-item {
    flex: 1;
    min-width: 0;
  }

  .footer-container {
    left: 0; // 移动端不需要减去菜单宽度
    padding: 12px 16px;
    justify-content: center; // 移动端居中显示
  }
}
:deep(.ant-spin-nested-loading){
  height: 100%;
  .ant-spin-container{
    height: 100%;
  }
}
</style>
