<script lang="ts" setup>
// 方案视图
import { ref, onMounted, useAttrs, inject, watch, watchEffect, provide, reactive } from 'vue';
import { Modal, message } from 'ant-design-vue';
import { useRouter, useRoute } from 'vue-router';
import { getDealTime, routerParam, resolveParam } from '@haierbusiness-front/utils';

import Scheme from '@haierbusiness-front/components/mice/scheme/component/index.vue';
import finalNode from '@haierbusiness-front/components/mice/orderList/finalNode.vue';

const router = useRouter();
const route = useRoute();

const footerChecked = ref('b'); // 底部检查
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab'); // 是否关闭当前页签

const schemeRef = ref(null); // 方案
const btnLoading = ref(true); // 按钮加载

// 方案视图
const handleSchemeView = (type) => {
  // 方案视图
  let query = {
    ...route.query,
    type: type,
  };

  if (route.path === '/bidman/scheme/index') {
    // 跳转方案视图
    router.push({ path: '/bidman/scheme/view', query: query });
  } else if (route.path === '/bidman/scheme/confirm') {
    // 跳转方案确认视图
    if (resolveParam(route.query.record).orderType === 'detail') {
      if (type === 'a') {
        // 跳转方案确认视图
        router.push({ path: '/bidman/scheme/confirm/view', query: query });
      } else if (type !== footerChecked.value && ['b', 'c'].includes(type)) {
        // 跳转方案确认视图
        router.push({ path: '/bidman/scheme/confirm', query: query });

        setTimeout(() => {
          schemeRef.value?.getList();
          // isCloseLastTab.value = true;
        }, 1000);
      }
      // isCloseLastTab.value = true;
      return;
    }

    // 跳转方案确认视图
    router.push({ path: '/bidman/scheme/confirm/view', query: query });
  } else if (route.path === '/bidman/bid/index') {
    // 跳转竞价视图
    router.push({ path: '/bidman/bid/view', query: query });
  }
  // isCloseLastTab.value = true;
};

// 保存方案
const saveSchemeH = () => {
  if (route.path === '/bidman/scheme/confirm') {
    // 保存方案确认
    schemeRef.value.saveSchemeConfirm();
  } else if (route.path === '/bidman/scheme/index') {
    // 保存方案
    Modal.confirm({
      title: '方案审核',
      content: '是否确认提交？',
      onOk() {
        schemeRef.value.saveScheme();
      },
      onCancel() {},
    });
  } else if (route.path === '/bidman/bid/index') {
    schemeRef.value.saveScheme();
  }
};

// 显示按钮
const showBtnT = ref(true);

// 显示按钮
const showBtn = () => {
  let text = '';

  if (route.path === '/bidman/scheme/index') {
    // 提交
    text = '提交';
  } else if (route.path === '/bidman/scheme/confirm') {
    // 方案确认
    text = '方案确认';
  } else if (route.path === '/bidman/bid/index') {
    // 发布竞价
    text = '发布竞价';
  }

  return text;
};

const hideBtn = ref('0'); // 隐藏按钮
const frameModel = ref(inject<any>('frameModel')); // 框架模型
const $attrs = useAttrs(); // 属性

// 返回订单列表
const backupOrderList = () => {
  if ($attrs.orderSource === 'manage') {
    // 管理端
    // 跳转订单列表
    router.push('/bidman/orderList/index');

    // 关闭当前页签
    isCloseLastTab.value = true;
  } else {
    // 用户端
    const localUrl = window.location.href;

    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';

    // 跳转需求确认页面
    const url = businessMiceBid + '/card-order/miceOrder?record=' + routerParam(resolveParam(route.query.record));
    window.location.href = url;
  }
};

// 竞价视图
const arrConfirmView = ref([
  'COST_APPROVAL',
  'MICE_PENDING',
  'MICE_EXECUTION',
  'MICE_COMPLETED',
  'BILL_CONFIRM',
  'BILL_APPROVAL',
  'BILL_RE_APPROVAL',
  'PAYMENT_CONFIRM',
  'PLATFORM_INVOICE_ENTRY',
  'VENDOR_INVOICE_ENTRY',
  'INVOICE_CONFIRM',
  'PLATFORM_REFUND_RECEIPT_UPLOAD',
  'REFUND_CONFIRM',
  'PLATFORM_PAY_RECEIPT_UPLOAD',
  'PLATFORM_INVOICE_CONFIRM',
  'SETTLEMENT_PENDING',
  'SETTLEMENT_RECORDED',
  'END',
]);

const schemeSumInitiate = ref([]); // 方案初始化
provide('schemeSumInitiate', schemeSumInitiate); // 提供方案初始化

// 方案初始化
watch(
  schemeSumInitiate,
  (newVal) => {
    if (newVal) {
      newVal.forEach((item) => {
        if (!item.isExclude) showBtnT.value = false;
      });
    }

    if (newVal.length === 0) showBtnT.value = true;

    btnLoading.value = false;
  },
  { deep: true },
);

const record = reactive({}); // 记录

// 记录
onMounted(() => {
  if (route.query.record.includes('%')) Object.assign(record, resolveParam(route.query.record));
  else {
    if (typeof route.query.record == 'string') Object.assign(record, JSON.parse(route.query.record));
  }
  footerChecked.value = route.query.type || 'b';
  hideBtn.value = record?.hideBtn || '0';
  frameModel.value = hideBtn.value === '1' ? 1 : 0;
});
</script>

<template>
  <div class="container">
    <Scheme v-bind="$attrs" ref="schemeRef" :class="record.hideBtn == '1' ? 'footer-user-width' : ''">
      <!-- 底部插槽：操作按钮组 -->
      <template #footer v-if="!btnLoading">
        <a-radio-group v-model:value="footerChecked" button-style="solid">
          <a-radio-button @click="handleSchemeView('a')" value="a">需求视图</a-radio-button>
          <a-radio-button @click="handleSchemeView('b')" value="b">方案视图</a-radio-button>
          <a-radio-button
            v-if="
              ['BID_RESULT_CONFIRM'].concat(arrConfirmView).includes(schemeRef?.miceDetail?.processNode) &&
              resolveParam(route.query.record).orderType === 'detail'
            "
            @click="handleSchemeView('c')"
            value="c"
            >竞价视图</a-radio-button
          >
        </a-radio-group>
        <a-button
          v-if="
            (hideBtn !== '1' && ['/bidman/scheme/confirm'].includes(route.path) && $attrs.orderSource === 'manage') ||
            resolveParam(route.query.record).orderType === 'detail' ||
            (showBtnT && ['SCHEME_APPROVAL'].includes(schemeRef?.miceDetail?.processNode))
          "
          style="float: right"
          @click="backupOrderList()"
          type="primary"
          >返回</a-button
        >
        <a-button v-show="hideBtn !== '1'" v-else style="float: right" type="primary" @click="saveSchemeH">{{
          showBtn()
        }}</a-button>
      </template>
    </Scheme>
  </div>
</template>
<style lang="less" scoped>
.container {
  background: #f1f2f6;
  padding: 0 auto;
}
.footer-user-width {
  width: 1280px !important;
  left: calc(50% - 640px);
}
</style>
