<script lang="ts" setup>
// 方案视图 - 组件 - 用户端、平台端
import { Modal, message } from 'ant-design-vue';
import Advisors from '@haierbusiness-front/components/mice/advisors/index.vue';
import { ref, useAttrs, onMounted, inject } from 'vue';
import MeetingConsultantDrawer from '@haierbusiness-front/components/meetingConsultantDrawer/index.vue';
import { getDealTime, routerParam, resolveParam } from '@haierbusiness-front/utils';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { SearchData } from '@haierbusiness-front/common-libs';
import { useRouter, useRoute } from 'vue-router';
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';

import finalNode from '@haierbusiness-front/components/mice/orderList/finalNode.vue';
import orderLog from '@haierbusiness-front/components/mice/orderList/orderLog.vue';
import memorandum from '@haierbusiness-front/components/mice/orderList/memorandum.vue';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

const store = applicationStore();

const route = useRoute();
const router = useRouter();

// 状态变量定义
const consultantModalOpen = ref(false); // 会务顾问抽屉是否打开
const searchData = ref<SearchData[]>([]); // 顾问列表数据
const searchDataSum = ref<SearchData[]>([]); // 顾问列表数据备份
const openRejectModal = ref(false); // 驳回弹窗是否打开
const reason = ref(''); // 驳回原因

const footerChecked = ref('a'); // 底部按钮选中状态
const $attrs = useAttrs(); // 属性
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab'); // 是否关闭当前页签

// 确认驳回
const handleOk = async () => {
  if (!reason.value) {
    message.error('驳回原因不能为空');
    return;
  }

  const res = await miceBidManOrderListApi.receive_reject({
    miceId: route.query.miceId,
    demandRejectReason: reason.value,
  });

  if (res.success) {
    message.success('驳回成功');
    openRejectModal.value = false;

    // 关闭当前页签
    isCloseLastTab.value = true;
    // 跳转订单列表页
    router.push('/bidman/orderList/index');
  }
};

// 分配人员
const assignPerson = async (item) => {
  // 分配人员
  const res = await miceBidManOrderListApi.userAssign({
    username: item.username,
    id: route.query.miceId,
  });

  if (res.success) {
    message.success('分配成功');
    consultantModalOpen.value = false;

    // 关闭当前页签
    isCloseLastTab.value = true;

    // 跳转订单列表页
    router.push('/bidman/orderList/index');
  }
};

// 搜索关键词
const searchKeyword = (value) => {
  if (value) {
    // 过滤顾问列表
    searchData.value = searchDataSum.value.filter((item) => {
      if (item.username.includes(value) || item.name.includes(value)) return item;
    })
  } else {
    // 重置顾问列表
    searchData.value = searchDataSum.value;
  }
};

// 方案视图
const handleScheme = (type) => {
  // 跳转方案视图
  let query = {
    miceId: route.query?.miceId,
    miceDemandId: route.query?.miceDemandId,
    type: type,
    record: route.query?.record,
  };

  if (route.path === '/bidman/scheme/view') {
    // 跳转方案视图
    router.push({ path: '/bidman/scheme/index', query: query });
  } else if (route.path === '/bidman/scheme/confirm/view') {
    // 跳转方案确认视图
    router.push({ path: '/bidman/scheme/confirm', query: query });
  } else if (route.path === '/bidman/bid/view') {
    // 跳转竞价视图
    router.push({ path: '/bidman/bid/index', query: query });
  }

  // 关闭当前页签
  isCloseLastTab.value = true;
};

const advisorsView = ref(null); // 顾问列表视图
const hideBtn = ref('0'); // 隐藏按钮
const frameModel = ref(inject<any>('frameModel')); // 框架模型
const record = resolveParam(route.query.record); // 记录

// 返回订单列表
const backupOrderList = () => {
  // 返回订单列表
  if ($attrs.orderSource === 'manage') {
    // 关闭当前页签
    isCloseLastTab.value = true;

    // 跳转订单列表页
    router.push('/bidman/orderList/index');
  } else {
    const localUrl = window.location.href;

    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';

    // 跳转需求确认页面
    const url = businessMiceBid + '/card-order/miceOrder?record=' + routerParam(resolveParam(route.query.record));

    // 跳转需求确认页面
    window.location.href = url;
  }
};

// 查看流程节点
const showProcess = ref<boolean>(false);

// 查看流程节点
const viewProcess = () => {
  // 查看流程节点
  showProcess.value = !showProcess.value;
};

// 备忘录
const viewMemo = () => {
  // 查看备忘录
  memorandumvisible.value = true;
};

// 日志
const viewLog = () => {
  // 查看日志
  isLogVisible.value = true;
};

// 竞价视图
const arrConfirmView = ref([
  'COST_APPROVAL',
  'MICE_PENDING',
  'MICE_EXECUTION',
  'MICE_COMPLETED',
  'BILL_CONFIRM',
  'BILL_APPROVAL',
  'BILL_RE_APPROVAL',
  'PAYMENT_CONFIRM',
  'PLATFORM_INVOICE_ENTRY',
  'VENDOR_INVOICE_ENTRY',
  'INVOICE_CONFIRM',
  'PLATFORM_REFUND_RECEIPT_UPLOAD',
  'REFUND_CONFIRM',
  'PLATFORM_PAY_RECEIPT_UPLOAD',
  'PLATFORM_INVOICE_CONFIRM',
  'SETTLEMENT_PENDING',
  'SETTLEMENT_RECORDED',
  'END',
]);

const isLogVisible = ref<boolean>(false); // 日志是否打开
const memoVisible = ref<boolean>(false); // 备忘录是否打开
const confirmLoading = ref(false); // 确认加载
const isViewLog = ref(false); // 是否查看日志

// 确认按钮
const onConfirmBtn = (num) => {
  // 1,日志
  if (num == 1) {
    isLogVisible.value = false;
  // 2,备忘录
  } else {
    memoVisible.value = false;
  }
};

// 获取权限
const getPermission = () => {
  // 权限
  store.loginUser.authorities.forEach((item) => {
    if (
      item.authority == import.meta.env.VITE_BUSINESS_CONSULTANT ||
      item.authority == import.meta.env.VITE_BUSINESS_MANAGE
    ) {
      // 是否查看日志
      isViewLog.value = true;
    }
  });
};

onMounted(() => {
  // 隐藏按钮
  hideBtn.value = record?.hideBtn || '0';
  // 框架模型
  frameModel.value = hideBtn.value === '1' ? 1 : 0;
  // 获取权限
  getPermission();
});

</script>
<template>
  <!-- 顾问列表页面主容器 -->
  <div class="container">
    <!-- 顾问列表组件 -->
    <Advisors
      v-bind="$attrs"
      preview-source="demandOne"
      ref="advisorsView"
      :class="record.hideBtn == '1' ? 'footer-user-width' : ''"
    >
      <!-- 头部插槽：备忘录和日志按钮 -->
      <template #header>
        <div v-if="hideBtn !== '1'">
          <a-button class="mr10" size="small" type="link" @click="viewProcess()">
            {{ showProcess ? '收起流程节点' : '查看流程节点' }}
            <UpOutlined v-if="!showProcess" />
            <DownOutlined v-else />
          </a-button>
        </div>
        <a-button
          v-if="isViewLog && $attrs.orderSource === 'manage' && !record.isBanner"
          size="small"
          style="margin-right: 10px"
          @click="viewMemo()"
        >
          备忘录
        </a-button>
        <a-button
          v-if="isViewLog && $attrs.orderSource === 'manage' && !record.isBanner"
          size="small"
          type="primary"
          style="margin-right: 10px"
          @click="viewLog()"
        >
          日志
        </a-button>
      </template>
      <!-- 流程节点 -->
      <template #processSlot>
        <!-- 流程信息 -->
        <div class="" v-show="showProcess">
          <finalNode :nodeId="route.query.record" :key="route.fullPath"></finalNode>
        </div>
      </template>

      <!-- 底部插槽：操作按钮组 -->
      <template #footer>
        <a-radio-group v-model:value="footerChecked" button-style="solid">
          <a-radio-button value="a">需求视图</a-radio-button>
          <a-radio-button @click="handleScheme('b')" value="b">方案视图</a-radio-button>
          <a-radio-button
            v-if="
              ['BID_RESULT_CONFIRM'].concat(arrConfirmView).includes(advisorsView?.orderDetail?.processNode) &&
              resolveParam(route.query.record).orderType === 'detail'
            "
            @click="handleScheme('c')"
            value="c"
            >竞价视图</a-radio-button
          >
        </a-radio-group>
        <a-button
          v-if="
            (hideBtn !== '1' &&
              ['/bidman/scheme/confirm/view'].includes(route.path) &&
              $attrs.orderSource === 'manage') ||
            resolveParam(route.query.record).orderType === 'detail'
          "
          style="float: right"
          @click="backupOrderList()"
          type="primary"
          >返回</a-button
        >
        <a-button v-show="hideBtn !== '1'" v-else style="float: right" @click="handleScheme('b')" type="primary"
          >下一步</a-button
        >
      </template>
    </Advisors>

    <!-- 驳回原因弹窗 -->
    <a-modal v-model:open="openRejectModal" title="驳回" @ok="handleOk">
      <p>驳回原因</p>
      <a-textarea v-model:value="reason" :rows="4" :maxLength="200" placeholder="请输入驳回原因" />
    </a-modal>

    <!-- 会务顾问抽屉组件 -->
    <meeting-consultant-drawer
      title="会务顾问"
      v-model="consultantModalOpen"
      class="meeting-consultant-drawer"
      :items="searchData"
      @search="searchKeyword"
      @assign="assignPerson"
    />
    <!-- 日志弹窗 -->
    <a-modal
      :visible="isLogVisible"
      width="70%"
      :confirmLoading="confirmLoading"
      @ok="onConfirmBtn(1)"
      @cancel="isLogVisible = false"
      title="日志"
    >
      <orderLog :nodeId="route.query.record" />
      <template #footer>
        <a-button @click="isLogVisible = false">关闭</a-button>
      </template>
    </a-modal>
    <!-- 备忘录弹窗 -->
    <a-modal
      :visible="memorandumvisible"
      width="70%"
      :confirmLoading="confirmLoading"
      @ok="onConfirmBtn(2)"
      @cancel="memorandumvisible = false"
      title="备忘录"
    >
      <memorandum :nodeId="route.query.record" />
      <template #footer>
        <a-button @click="memorandumvisible = false">关闭</a-button>
      </template>
    </a-modal>
  </div>
</template>
<style lang="less" scoped>
.container {
  background: #f1f2f6;
  padding: 0 auto;
}

.footer-user-width {
  width: 1280px !important;
  left: calc(50% - 640px);
}

:deep(.ant-btn-default) {
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  text-align: center;
  font-style: normal;
}

:deep(.ant-btn-primary) {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  padding: 3px 8px;
  height: 32px;
  background: #1868db;
  border-radius: 2px;
}

.reject-btn {
  background: #f5222d;
}

:deep(.demand_contrast_footer) {
  text-align: left !important;
}

:where(.css-dev-only-do-not-override-bq26c2).ant-btn > span + .anticon {
  margin-inline-start: 2px;
}
</style>
