<script lang="ts" setup>
// 需求发布预览页面 - 用户端
import { message } from 'ant-design-vue'
import Advisors from '@haierbusiness-front/components/mice/advisors/index.vue'
import { ref } from 'vue'
import MeetingConsultantDrawer from '@haierbusiness-front/components/meetingConsultantDrawer/index.vue'

import { miceBidManOrderListApi } from '@haierbusiness-front/apis'
import { SearchData } from '@haierbusiness-front/common-libs'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 状态变量定义
const consultantModalOpen = ref(false) // 会务顾问抽屉是否打开
const searchData = ref<SearchData[]>([]) // 顾问列表数据
const searchDataSum = ref<SearchData[]>([]) // 顾问列表数据备份
const rejectModalOpen = ref(false) // 驳回原因弹窗是否打开
const demandRejectReason = ref('') // 驳回原因

// 驳回原因弹窗打开
const handleReject = () => {
  rejectModalOpen.value = true
}

// 分配人员
const assignPerson = async (item) => {
  const res = await miceBidManOrderListApi.userAssign({
    username: item.username,
    id: route.query.miceId,
  })

  if (res.success) {
    message.success('分配成功')
    consultantModalOpen.value = false
  }
}

// 处理发布
const handlePublish = () => {
  // 跳转需求发布页面
  router.push({
    path: '/bidman/publish/index',
    query: {
      record: JSON.stringify({ miceId: route.query.miceId, miceDemandId: route.query.miceDemandId, status: '1' }),
    },
  })
}

// 驳回原因
const rejectPush = async () => {
  if (demandRejectReason.value) {
    const res = await miceBidManOrderListApi.cancelPush({
      miceId: route.query.miceId,
      demandRejectReason: demandRejectReason.value,
    })

    if (res.success) {
      message.success('驳回成功')
      rejectModalOpen.value = false

      // 关闭抽屉
      consultantModalOpen.value = false

      // 跳转订单列表
      router.push({ path: '/bidman/orderList/index', query: { status: '0' } })
    }
  } else {
    message.error('驳回原因不能为空！')
  }
}

// 搜索关键词
const searchKeyword = (value) => {
  if (value) {
    // 过滤顾问列表
    searchData.value = searchDataSum.value.filter((item) => {
      if (item.username.includes(value) || item.name.includes(value)) {
        return item
      }
    })
  } else {
    // 重置顾问列表
    searchData.value = searchDataSum.value
  }
}
</script>

<template>
  <!-- 顾问列表页面主容器 -->
  <div>
    <!-- 顾问列表组件 -->
    <Advisors preview-source="demandOne">
      <!-- 头部插槽：备忘录和日志按钮 -->
      <template #header></template>
      <!-- 底部插槽：操作按钮组 -->
      <template #footer>
        <a-button danger size="small" style="margin-right: 10px" type="primary" @click="rejectModalOpen = true">需求驳回</a-button>
        <a-button size="small" style="margin-right: 10px" type="primary" @click="handlePublish">需求发布</a-button>
      </template>
    </Advisors>

    <!-- 驳回原因弹窗 -->
    <a-modal v-model:open="rejectModalOpen" title="驳回原因" @ok="rejectPush">
      <a-textarea v-model:value="demandRejectReason" :max-length="200" :rows="4" placeholder="请输入驳回原因" />
    </a-modal>

    <!-- 会务顾问抽屉组件 -->
    <meeting-consultant-drawer
      v-model="consultantModalOpen"
      :items="searchData"
      title="会务顾问"
      @assign="assignPerson"
      @search="searchKeyword"
    />
  </div>
</template>
<style lang="less" scoped>
:deep(.ant-btn-default) {
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  text-align: center;
  font-style: normal;
}

:deep(.ant-btn-primary) {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
}

.reject-btn {
  background: #f5222d;
}
</style>
