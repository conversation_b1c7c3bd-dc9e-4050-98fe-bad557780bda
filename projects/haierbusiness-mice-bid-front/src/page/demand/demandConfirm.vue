<script lang="ts" setup>
// 需求确认 - 用户端
import { inject, onMounted, ref } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { resolveParam } from '@haierbusiness-front/utils'

import { DemandSubmitObj } from '@haierbusiness-front/common-libs'
import { demandApi } from '@haierbusiness-front/apis'

import advisors from '@haierbusiness-front/components/mice/advisors/index.vue'

const route = useRoute()
const router = useRouter()
const isCloseLastTab = inject<ref<boolean>>('isCloseLastTab') // 是否关闭最后一个标签

const businessIndex = import.meta.env.VITE_BUSINESS_INDEX_URL + '#' // 业务首页
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL // 业务流程

const previewSource = ref<string>('demandContrast') // 需求交互，对比
const miceId = ref<string>('') // 会议单号
const hideBtn = ref<string>('') // 是否隐藏按钮
const demandRejectReason = ref<string>('') // 驳回原因
const approveCode = ref<string>('') // 审批流Code
const approvalModalShow = ref<boolean>(false) // 审批流弹窗
const refuseModalShow = ref<boolean>(false) // 驳回弹窗
const subLoading = ref<boolean>(false) // 提交中
const refuseLoading = ref<boolean>(false) // 驳回中

const sourceId = ref<number>(null) // 需求ID

// 驳回按钮
const refuseBtn = () => {
  demandRejectReason.value = '' // 驳回原因
  refuseModalShow.value = true // 驳回弹窗
}

// 驳回按钮 - 驳回
const handleRefuse = async () => {
  if (!demandRejectReason.value) {
    message.error('驳回原因不能为空！')
    return
  }

  refuseLoading.value = true

  demandApi
    .demandUserReject({ miceId: miceId.value, demandRejectReason: demandRejectReason.value })
    .then((res) => {
      message.success('需求已驳回')

      closeApproval()
    })
    .finally(() => {
      refuseLoading.value = false
    })
}

// 确认需求按钮 - 确认需求
const subDemand = async () => {
  Modal.confirm({
    title: '确定提交？',
    // icon: null,
    content: '',
    onOk: async () => {
      subLoading.value = true

      demandApi
        .demandUserConfirm({
          miceId: miceId.value,
          sourceId: sourceId.value,
        })
        .then((res) => {
          if (res.data) {
            approvalModalShow.value = true

            // 审批Code赋值
            approveCode.value = res.data.processCode

            return
          }

          // 没有返回审批流Code
          closeApproval()
        })
        .finally(() => {
          subLoading.value = false
        })
    },
    onCancel() {},
  })
}

// 关闭审批流
const closeApproval = () => {
  if (isCloseLastTab) {
    // 关闭当前页面
    isCloseLastTab.value = true
  }

  const url = businessIndex + '/card-order/miceOrder' // 跳转业务首页
  window.location.replace(url)
}

// 返回按钮
const backBtn = () => {
  router.go(-1) // 返回上一页
}

// 需求详情 - 需求详情
const demandDetailsEmit = (item: DemandSubmitObj) => {
  sourceId.value = item.id // 需求ID
}

onMounted(async () => {
  const record = resolveParam(route.query.record)
  miceId.value = record.miceId // 会议单号
  hideBtn.value = record.hideBtn || '' // 是否隐藏按钮
  console.log('%c [ record ]-需求确认', 'font-size:13px; background:pink; color:#bf2c9f;', record)
})
</script>

<template>
  <!-- 需求确认 -->
  <div class="wid1280 demand_preview">
    <advisors
      :is-manage-page="false"
      :platform-type="'user'"
      :preview-source="previewSource"
      @demandDetailsEmit="demandDetailsEmit"
    >
      <template #header></template>
      <template #footer>
        <a-button class="mr10" size="small" @click="backBtn()">返回</a-button>
        <div v-if="hideBtn !== '1'" style="display: inline-block">
          <a-button class="mr10" danger size="small" type="primary" @click="refuseBtn()">需求驳回</a-button>
          <a-button :loading="subLoading" size="small" type="primary" @click="subDemand()">确认需求</a-button>
        </div>
      </template>
    </advisors>

    <!-- 审批流 -->
    <a-modal
      v-model:open="approvalModalShow"
      :closable="false"
      :keyboard="false"
      :mask-closable="false"
      title="已提交如下人员审批"
      width="80%"
    >
      <div>
        <iframe :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" frameborder="0" width="100%"></iframe>
      </div>
      <template #footer>
        <a-button @click="closeApproval">确定</a-button>
      </template>
    </a-modal>
    <!-- 驳回 -->
    <a-modal
      v-model:open="refuseModalShow"
      :confirm-loading="refuseLoading"
      title="驳回原因"
      width="600px"
      @ok="handleRefuse"
    >
      <div>
        <a-textarea
          v-model:value="demandRejectReason"
          :maxlength="500"
          class="mt10 mb10"
          placeholder="请填写驳回原因"
        />
      </div>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
.demand_preview {
}
</style>
