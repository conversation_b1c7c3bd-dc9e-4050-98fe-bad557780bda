<script lang="ts" setup>
// 标的方案 - 用户端
import Scheme from '@haierbusiness-front/components/mice/scheme/index.vue'
import SchemeView from '@haierbusiness-front/components/mice/scheme/view.vue'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const isView = computed(() => route.path === '/bidman/scheme/confirm/view') // 是否查看
</script>

<template>
  <div class="h-full">
    <div class="container">
      <SchemeView v-if="isView" order-source="user" platform-type="user"></SchemeView>
      <Scheme v-else order-source="user"></Scheme>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.h-full {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .container {
    width: 1280px;
  }
}
</style>
