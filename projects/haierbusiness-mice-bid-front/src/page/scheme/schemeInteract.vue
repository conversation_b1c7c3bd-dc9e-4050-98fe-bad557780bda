<script lang="ts" setup>
import { inject, onMounted, reactive, ref } from 'vue'
import schemeInteract from '@haierbusiness-front/components/scheme/schemeInteract.vue'
import { resolveParam } from '@haierbusiness-front/utils'
import { message, Modal } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import { miceBidManOrderListApi } from '@haierbusiness-front/apis'

const route = useRoute()
const frameModel = ref(inject<any>('frameModel')) // 框架模型
const isCloseLastTab = inject<ref<boolean>>('isCloseLastTab') // 是否关闭最后一个标签

const rejectModalShow = ref(false) // 驳回弹窗
const rejectReason = ref<string>('') // 驳回原因

// 路由参数
const routeQuery = reactive({
  record: resolveParam(route.query.record) || JSON.parse(route.query.record),
})

// 礼品确认按钮
const presentConfirm = async () => {
  Modal.confirm({
    title: '礼品确认',
    content: '下单礼品是否确认？',
    async onOk() {
      const res = await miceBidManOrderListApi.infoConfirm({
        mainCode: routeQuery.record.mainCode,
        confirmStatus: 1, // 确认状态
        rejectReason: '', // 驳回原因
      })

      if (res.success) {
        message.success('确认成功！')

        const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#'
        // 跳转需求确认页面
        const url =
          (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
          '/card-order/miceOrder'
        window.location.href = url

        // 关闭当前标签页
        if (isCloseLastTab) {
          isCloseLastTab.value = true
        }
      }
    },
    onCancel() {},
  })
}

// 驳回按钮 - 驳回
const cancelConfirm = async () => {
  if (rejectReason.value === '') {
    message.error('驳回原因不能为空！')
    return
  }

  // 驳回
  const res = await miceBidManOrderListApi.infoConfirm({
    mainCode: routeQuery.record.mainCode,
    confirmStatus: 3,
    rejectReason: rejectReason.value,
  })

  if (res.success) {
    message.success('驳回成功！')
    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#'
    // 跳转需求确认页面
    const url =
      (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
      '/card-order/miceOrder'
    window.location.href = url

    // 关闭当前标签页
    if (isCloseLastTab) {
      isCloseLastTab.value = true
    }
  }
}

onMounted(() => {
  frameModel.value = routeQuery.record.hideBtn === '1' ? 1 : 0 // 框架模型
})
</script>

<template>
  <div class="container">
    <!-- 方案互动 -->
    <schemeInteract />
    <div class="footer">
      <a-button danger style="margin-right: 20px" type="primary" @click="rejectModalShow = true">驳回</a-button>
      <a-button type="primary" @click="presentConfirm">确认</a-button>
      <a-modal v-model:open="rejectModalShow" title="驳回原因" @ok="cancelConfirm">
        <a-textarea v-model:value="rejectReason" :max-length="200" :rows="4" placeholder="请输入驳回原因" />
      </a-modal>
    </div>
  </div>
</template>

<style lang="less" scoped>
.container {
  padding-bottom: 40px;
}

.footer {
  border-top: 1px solid #ccc;
  right: 0;
  background: #fff;
  z-index: 11;
  width: 100%;
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  text-align: right;
}
</style>
