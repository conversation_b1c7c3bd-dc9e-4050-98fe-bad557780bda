<script lang="ts" setup>
// 方案变更确认 - 用户端
import { ref, inject, onMounted, reactive } from 'vue'
import { resolveParam } from '@haierbusiness-front/utils'
import { schemeApi } from '@haierbusiness-front/apis'
import { useRoute } from 'vue-router'

const route = useRoute()
const frameModel = ref(inject<any>('frameModel')) // 框架模型

// 路由参数
const routeQuery = reactive({
  record: resolveParam(route.query.record) || JSON.parse(route.query.record),
})

// 方案变更列
const priceChangeColumnsScheme = ref([
  {
    title: '切换前服务商',
    dataIndex: 'merchantName',
    width: 280,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '切换后服务商',
    dataIndex: 'merchantName',
    width: 280,
    ellipsis: true,
    align: 'center',
  },
  {
    title: '变更说明',
    dataIndex: 'reason',
    // width: 160,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '附件',
    dataIndex: 'doc',
    align: 'center',
    ellipsis: true,
  },
])

const priceChangeListScheme = ref([]) // 方案变更列表

// 显示方案变更弹窗
const showSwitchBidSchemeModal = async () => {
  // 调用接口，获取方案变更列表
  const res = await schemeApi.switchRecord({
    miceId: routeQuery.record.miceId,
  })

  // 将方案变更列表转换为表格数据
  priceChangeListScheme.value = res.slice(0, 1).map((item) => {
    let str = ''
    let document = {}

    // 将方案变更列表转换为表格数据
    item.schemeBidSwitchPaths.forEach((item1, index) => {
      try {
        document = JSON.parse(item1)
      } catch (error) {
        console.log(error)
      }

      str += `<a target='_blank' href='${document.url}'>${
        document.name
      }</a><span style='margin-right: 10px;color: #86909c' >${
        index == item.schemeBidSwitchPaths?.length - 1 ? '' : ','
      }</span>`
    })

    item.doc = str
    return item
  })
}

onMounted(() => {
  frameModel.value = routeQuery.record.hideBtn === '1' ? 1 : 0
  showSwitchBidSchemeModal() // 显示方案变更弹窗
})
</script>

<template>
  <div class="container">
    <div class="changeScheme" style="margin-bottom: 16px">
      <a-table
        :columns="priceChangeColumnsScheme"
        :data-source="priceChangeListScheme"
        :pagination="false"
        :row-key="(record: { id: string }) => record.id"
        bordered
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <span v-html="record[column.dataIndex]"></span>
        </template>
      </a-table>
    </div>
  </div>
</template>

<style lang="less" scoped>
.container {
  background: #f1f2f6;
  padding: 0 auto;
  height: calc(100vh - 60px);
}

.changeScheme {
  background: #fff;
  margin: 0 auto;
  width: 1280px !important;
  left: calc(50% - 640px);
  height: 100%;
}

.footer {
  border-top: 1px solid #f1f2f6;
  box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
  right: 0;
  background: #fff;
  left: calc(50% - 640px);
  z-index: 11;
  width: 1280px !important;
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
}

:deep(.ant-table-thead) {
  th {
    color: #86909c !important;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    font-style: normal;
  }
}

:deep(.ant-table-cell) {
  padding: 12px 8px !important;
  line-height: 20px;
}
</style>
