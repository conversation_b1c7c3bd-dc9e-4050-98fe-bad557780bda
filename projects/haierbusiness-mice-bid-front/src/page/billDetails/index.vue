<script lang="ts" setup>
// 账单详情 - 用户端
import Scheme from '@haierbusiness-front/components/mice/scheme/index.vue'
import SchemeView from '@haierbusiness-front/components/mice/scheme/view.vue'
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const isView = computed(() => route.path === '/bidman/scheme/confirm/view') // 是否查看
</script>

<template>
  <div>
    <SchemeView v-if="isView" order-source="user"></SchemeView>
    <Scheme v-else order-source="user"></Scheme>
  </div>
</template>
