<script lang="ts" setup>
// 账单确认 - 用户端
import { reactive, ref } from 'vue'
import { resolveParam } from '@haierbusiness-front/utils'
import { message, Modal } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import { miceBidManOrderListApi } from '@haierbusiness-front/apis'

import meetingDetail from '@haierbusiness-front/components/mice/orderList/meetingDetail.vue'
import billUploadScheme from '@haierbusiness-front/components/billUploadScheme/billUploadschemeDetails.vue'

const route = useRoute()
const isCloseLastTab = inject<ref<boolean>>('isCloseLastTab') // 是否关闭最后一个标签

// 路由参数
const routeQuery = reactive({
  record: resolveParam(route.query.record) || JSON.parse(route.query.record),
})

const viewSelect = ref('demand') // 视图选择
const billUploadSchemeRef = ref() // 账单视图

// 确认按钮 - 确认
const presentConfirm = async () => {
  Modal.confirm({
    title: '账单确认',
    content: '是否确认全部账单？',
    async onOk() {
      const res = await miceBidManOrderListApi.userConfirm({
        miceId: routeQuery.record.miceId,
        confirmState: 1,
      })

      if (res.success) {
        message.success('确认成功！')

        const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#'
        // 跳转需求确认页面
        const url =
          (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
          '/card-order/miceOrder'
        window.location.href = url

        // 关闭当前标签页
        if (isCloseLastTab) {
          isCloseLastTab.value = true
        }
      }
    },
    onCancel() {},
  })
}

const rejectModalShow = ref(false) // 驳回弹窗
const reason = ref<string>('') // 驳回原因

const rejectList = ref() // 驳回列表
const rejectColumns = [
  { title: '驳回', dataIndex: 'checked', key: 'checked', width: 100 },
  { title: '服务商', dataIndex: 'provider', key: 'provider', width: 200 },
  { title: '驳回原因', dataIndex: 'reason', key: 'reason' },
]

// 获取驳回列表
const getRejectList = async () => {
  rejectList.value = billUploadSchemeRef.value.tempDataList
  rejectModalShow.value = true
}

// 驳回按钮 - 驳回
const cancelConfirm = async () => {
  // 获取已选中的驳回列表
  const selected = rejectList.value.filter((item) => item.checked)

  // 如果已选中的驳回列表为空，则提示请至少选择一个需要驳回的服务商
  if (selected.length === 0) {
    message.error('请至少选择一个需要驳回的服务商！')
    return
  }

  // 如果已选中的驳回列表中存在没有驳回原因的服务商，则提示请填写所有已选服务商的驳回原因
  if (selected.some((item) => !item.reason)) {
    message.error('请填写所有已选服务商的驳回原因！')
    return
  }

  // 将已选中的驳回列表转换为驳回账单列表
  const rejectBills = selected.map((item) => ({
    rejectBillId: item.id,
    reason: item.reason,
  }))

  // 调用接口，驳回账单
  const res = await miceBidManOrderListApi.userConfirm({
    miceId: routeQuery.record.miceId,
    confirmState: 2,
    rejectBills: rejectBills,
  })

  // 如果驳回成功，则提示确认成功
  if (res.success) {
    message.success('确认成功！')
    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#'
    // 跳转需求确认页面
    const url =
      (window.location.href.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
      '/card-order/miceOrder'
    window.location.href = url

    // 关闭当前标签页
    if (isCloseLastTab) {
      isCloseLastTab.value = true
    }
  }

  // 关闭驳回弹窗
  rejectModalShow.value = false
}
</script>

<template>
  <div :style="viewSelect === 'billUpload' ? 'padding-bottom:40px' : ''" class="container">
    <!-- 方案互动 -->
    <meetingDetail
      v-if="viewSelect === 'demand'"
      :class="routeQuery.record.hideBtn == '1' ? 'footer-user-width' : ''"
      type="user"
    />

    <!-- 账单视图 -->
    <billUploadScheme
      v-else-if="viewSelect === 'billUpload'"
      ref="billUploadSchemeRef"
      :class="routeQuery.record.hideBtn == '1' ? 'footer-user-width' : ''"
      :platform-type="'user'"
    ></billUploadScheme>

    <!-- 底部 -->
    <div class="footer">
      <a-radio-group v-model:value="viewSelect" button-style="solid">
        <a-radio-button value="demand">需求视图</a-radio-button>
        <a-radio-button value="billUpload">账单视图</a-radio-button>
      </a-radio-group>

      <!-- 需求视图 -->
      <div v-if="viewSelect === 'demand'">
        <a-button type="primary" @click="viewSelect = 'billUpload'">下一步</a-button>
      </div>

      <!-- 账单视图 -->
      <div v-else>
        <a-button danger style="margin-right: 20px" type="primary" @click="getRejectList()">驳回</a-button>
        <a-button type="primary" @click="presentConfirm">确认</a-button>
      </div>

      <!-- 驳回弹窗 -->
      <a-modal v-model:open="rejectModalShow" title="驳回原因" width="1200px" @ok="cancelConfirm">
        <a-table :columns="rejectColumns" :data-source="rejectList" :pagination="false" row-key="id">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'checked'">
              <a-checkbox v-model:checked="record.checked" />
            </template>
            <template v-else-if="column.dataIndex === 'provider'">
              {{ record.merchantName }}
            </template>
            <template v-else-if="column.dataIndex === 'reason'">
              <a-input v-model:value="record.reason" :bordered="false" placeholder="请输入驳回原因" />
            </template>
          </template>
        </a-table>
      </a-modal>
    </div>
  </div>
</template>

<style lang="less" scoped>
.container {
  background: #f1f2f6;
  padding: 0 auto;
}

.footer-user-width {
  margin: auto;
  width: 1280px !important;
  left: calc(50% - 640px);
}

.footer {
  border-top: 1px solid #f1f2f6;
  box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.03);
  right: 0;
  background: #fff;
  left: calc(50% - 640px);
  z-index: 11;
  width: 1280px !important;
  padding: 10px 20px;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
}

:deep(.ant-table-thead) {
  th {
    color: #86909c !important;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    font-style: normal;
  }
}

:deep(.ant-table-cell) {
  padding: 12px 8px !important;
  line-height: 20px;
}
</style>
