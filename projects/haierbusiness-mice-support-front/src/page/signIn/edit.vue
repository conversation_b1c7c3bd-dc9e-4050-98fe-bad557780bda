<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import dayjs, { Dayjs } from 'dayjs'
import { meetingSignInApi } from '@haierbusiness-front/apis'
import {
  IMeetingSignInRules,
} from '@haierbusiness-front/common-libs'
// @ts-ignore T-000
import signInMap from '../components/signInMap/index.vue'
import { message } from 'ant-design-vue'
//解决警告
const props = defineProps({
  query: {
    type: Object,
    default: () => ({}),
  },
})
const route = useRoute()
const router = useRouter()
// 从URL获取会议信息
const miceInfoId = route.query.miceInfoId
const miceInfoName = route.query.miceInfoName
const loading = ref(false)
// 处理boolen => number
const typeChange = (res: boolean | undefined | null | number) => {
  if (res) {
    return 1
  } else if (res == false) {
    return 0
  } else {
    return null
  }
}

//签到规则
const signInRules = ref<IMeetingSignInRules>({
  checkInStartDate: '',
  checkInEndDate: '',
  checkInRuleMethod: [],
  isOpenCheckIn: 1,
  checkInCodeUrl: '',
  checkInHandleNickName: '',
  checkInHandleUsername: '',
  isCheckInNotice: 1,
  checkInRange: 0,
  checkInCodeId: 0,
  checkInCodeBgUrl: '',
})
//地图相关
const mapRef = ref()
//地图按钮显示
const mapShow = ref(0)
const currentLocation = ref({
  address: '',
  name: '',
  city: '',
  district: '',
})
const formRef = ref()
const labelCol = { span: 5 }
const wrapperCol = { span: 13 }

//获取签到规则
const signInRulesMethod = async () => {
  const currentMiceInfoId = miceInfoId
  const response = await meetingSignInApi.details(Number(currentMiceInfoId) || 2)
  if (response) {
    signInRules.value = response
    console.log(signInRules.value, 'signInRules.value')
    console.log('签到位置详情:', signInRules.value?.checkList)
    //进行类型转换解决警告
    signInRules.value.isOpen = typeChange(signInRules.value.isOpen)
    signInRules.value.isOpenCheckIn = typeChange(signInRules.value.isOpenCheckIn)
    signInRules.value.isCheckInNotice = typeChange(signInRules.value.isCheckInNotice)
    signInRules.value.checkInRuleMethod = signInRules.value.checkInRuleMethod?.toString().split(',')
    signInRules.value.validDate = (
      signInRules.value.checkInStartDate && signInRules.value.checkInEndDate
    ) ? [
      dayjs(signInRules.value.checkInStartDate),
      dayjs(signInRules.value.checkInEndDate)
    ] : null;
    // 初始化地图
    if (signInRules.value?.checkList?.[0]?.longitude && signInRules.value?.checkList?.[0]?.latitude) {
      let lon = signInRules.value.checkList[0].longitude
      let lat = signInRules.value.checkList[0].latitude
      console.log(lon, lat);
      if (mapRef.value && (lon && lat)) {
        mapRef.value.createByMap(Number(lon), Number(lat))
        setTimeout(() => {
          mapRef.value.addMarker(Number(lon), Number(lat))
        }, 1000)
      }

    }
  }
}
//表单验证
const rules = {
  isOpen: [{ required: true, message: '请选择是否开启签到', trigger: 'change' }],
  validDate: [{ required: true, message: '请选择有效日期', trigger: 'change' }],
  checkInRuleMethod: [{ required: true, message: '请选择签到方式', trigger: 'change' }],
  isOpenCheckIn: [{ required: true, message: '请选择小程序是否自动签到', trigger: 'change' }],
  checkInCodeUrl: [{ required: true, message: '请选择小程序是否显示', trigger: 'change' }],
  checkInHandleNickName: [{ required: true, message: '请选择签到负责人', trigger: 'blur' }],
  isCheckInNotice: [{ required: true, message: '请选择是否开启签到通知', trigger: 'blur' }],
  checkInCodeBgUrl: [{ required: true, message: '请选择签到二维码背景图设置方式', trigger: 'blur' }],
  checkInRange: [{ required: true, message: '请选择有效签到范围', trigger: 'blur' }],
}
//签到规则提交
const onSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      loading.value = true
      let params = {}
      const { id,isOpen,validDate, ...newSignInRules } = signInRules.value;
      const baseParams = id && isOpen !== 1
      ? { id, miceInfoId, isOpen }
      : { ...newSignInRules, miceInfoId };
      if (signInRules.value.id) {
        if (signInRules.value.isOpen == 1) {
          
          params = { ...newSignInRules, miceInfoId };
        } else {
          params = {
            id: signInRules.value.id,
            miceInfoId,
            isOpen: signInRules.value.isOpen
          }
        }
        console.log(params, "parmas");
        meetingSignInApi.edit(params)
          .then(() => {
            message.success('编辑规则成功')
            router.go(-1)
            loading.value = false
          })
          .catch(() => {
            message.error('编辑规则失败')
            loading.value = false
          })
          .finally(() => {
            loading.value = false
          })
      }else{
        const { id,validDate, ...newSignInRules } = signInRules.value;
        params = { ...newSignInRules, miceInfoId };
        meetingSignInApi.save(params)
          .then(() => {
            message.success('创建规则成功')
            router.go(-1)
            loading.value = false
          })
          .catch(() => {
            message.error('创建规则失败')
            loading.value = false
          })
          .finally(() => {
            loading.value = false
          })
      }

    })
}
//监听有效签到日期
watch(
  () => signInRules.value.validDate,
  (n: any) => {
    if (n) {
      signInRules.value.checkInStartDate = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
      signInRules.value.checkInEndDate = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
    } else {
      signInRules.value.checkInStartDate = undefined
      signInRules.value.checkInEndDate = undefined
    }
  },
)
//取消编辑
const resetForm = () => {
  router.go(-1)
  formRef.value.resetFields()
}
//签到范围
const Rangeoptions = [
  { value: 0.1, label: '距签到位置0.1公里以内' },
  { value: 0.5, label: '距签到位置0.5公里以内' },
  { value: 1, label: '距签到位置1公里以内' },
  { value: 1.5, label: '距签到位置1.5公里以内' },
  { value: 2, label: '距签到位置2公里以内' },
  { value: 3, label: '距签到位置3公里以内' },
]
//切换地图显示
const changeMap = (map: any, index: number) => {
  mapRef.value.createByMap(map.longitude, map.latitude)
  mapRef.value.addMarker(map.longitude, map.latitude)
  mapShow.value = index
}
//处理位置更新
const handleLocationUpdate = (locationData: any) => {
  currentLocation.value = locationData
  console.log('签到位置信息:', locationData)
}
//重新构建地图
const handleIsOpen = (item: number | undefined | boolean | null) => {
  if (item == 1 && signInRules.value) {
    const lon = signInRules.value?.checkList?.[0]?.longitude;
    const lat = signInRules.value?.checkList?.[0]?.latitude;
    if (mapRef.value && (lon && lat)) {
      mapRef.value.createByMap(Number(lon), Number(lat))
      setTimeout(() => {
        mapRef.value.addMarker(Number(lon), Number(lat))
      }, 1000)
    }
  }
}

onMounted(() => {
  signInRulesMethod()
})
</script>
<template>
  <div class="container">
    <div class="content">
      <div class="main">
        <div class="main-top">
          <div class="top-left">
            <h3>{{ signInRules.id ? '编辑' : '创建' }}签到规则</h3>
          </div>
          <div class="top-right">
            <a-button style="margin-right: 10px" @click="resetForm">取消{{ signInRules.id ? '编辑' : '创建' }}</a-button>
            <a-button type="primary" @click="onSubmit" :loading="loading">完成{{ signInRules.id ? '编辑' : '创建'
            }}</a-button>
          </div>
        </div>
        <div v-if="signInRules.id">
          <a-form ref="formRef" :label-col="labelCol" :model="signInRules" :rules="rules" :wrapper-col="wrapperCol"
            :hideRequiredMark="true" v-show="signInRules.isOpen == 1">
            <a-form-item ref="name" label="是否需要签到：" name="isOpen">
              <a-radio-group v-model:value="signInRules.isOpen" name="radioGroup"
                @change="handleIsOpen(signInRules.isOpen)">
                <a-radio :value="1">需要签到</a-radio>
                <a-radio :value="0">无需签到</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="有效签到日期" name="validDate">
              <a-range-picker v-model:value="signInRules.validDate" style="width: 100%" />
            </a-form-item>
            <a-form-item label="签到方式" name="checkInRuleMethod">
              <a-select ref="select" v-model:value="signInRules.checkInRuleMethod" mode="tags" placeholder="请选择签到方式">
                <a-select-option value="1">点击签到按钮签到</a-select-option>
                <a-select-option value="2">扫码签到</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="打开小程序自动签到" name="isOpenCheckIn">
              <a-select ref="select" v-model:value="signInRules.isOpenCheckIn" placeholder="请选择小程序是否自动签到">
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="签到二维码" name="checkInCodeUrl">
              <a-select ref="select" v-model:value="signInRules.checkInCodeUrl" placeholder="请选择小程序是否展示二维码">
                <a-select-option value="小程序首页显示">小程序首页显示</a-select-option>
                <a-select-option value="小程序首页不显示">小程序首页不显示</a-select-option>
              </a-select>
              <template> 如需专人检查入场凭证，建议开启 </template>
            </a-form-item>
            <a-form-item label="签到负责人" name="checkInHandleNickName">
              <a-input v-model:value="signInRules.checkInHandleNickName" disabled placeholder="请填写签到负责人"></a-input>
            </a-form-item>
            <a-form-item label="签到通知" name="isCheckInNotice">
              <a-select ref="select" v-model:value="signInRules.isCheckInNotice" placeholder="请选择是否开启签到通知">
                <a-select-option :value="1">开启</a-select-option>
                <a-select-option :value="0">关闭</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="签到二维码背景图" name="checkInCodeBgUrl">
              <a-select ref="select" v-model:value="signInRules.checkInCodeBgUrl" placeholder="请选择二维码背景图设置">
                <a-select-option value="内置模板">内置模板</a-select-option>
                <a-select-option value="自定义">自定义</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="有效签到范围" name="checkInRange">
              <a-select v-model:value="signInRules.checkInRange" :options="Rangeoptions"
                placeholder="请选择有效签到范围"></a-select>
            </a-form-item>
            <a-form-item label="签到位置：">
              <a-button :type="mapShow == index ? 'primary' : 'default'"
                v-for="(value, index) in signInRules?.checkList" :key="index" style="margin-right: 5px;"
                @click="changeMap(value, index)">{{ value.place }}</a-button>
            </a-form-item>
            <a-form-item label="位置地图：">
              <div class="map-container">
                <signInMap ref="mapRef" @locationUpdate="handleLocationUpdate" :SignInArea="signInRules?.checkInRange">
                </signInMap>
              </div>
            </a-form-item>
          </a-form>
          <a-form ref="formRef" :label-col="labelCol" :model="signInRules" :rules="rules" :wrapper-col="wrapperCol"
            v-show="signInRules.isOpen == 0" :hideRequiredMark="true">
            <a-form-item ref="name" label="是否需要签到：" name="isOpen">
              <a-radio-group v-model:value="signInRules.isOpen" name="radioGroup">
                <a-radio :value="1">需要签到</a-radio>
                <a-radio :value="0">无需签到</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>
        </div>
        <div v-if="!signInRules.id">
          <a-form ref="formRef" :label-col="labelCol" :model="signInRules" :rules="rules" :wrapper-col="wrapperCol"
            :hideRequiredMark="true">
            <a-form-item ref="name" label="是否需要签到：" name="isOpen">
              <a-radio-group v-model:value="signInRules.isOpen" name="radioGroup"
                @change="handleIsOpen(signInRules.isOpen)">
                <a-radio :value="1">需要签到</a-radio>
                <a-radio :value="0">无需签到</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item label="有效签到日期" name="validDate">
              <a-range-picker v-model:value="signInRules.validDate" style="width: 100%" />
            </a-form-item>
            <a-form-item label="签到方式" name="checkInRuleMethod">
              <a-select ref="select" v-model:value="signInRules.checkInRuleMethod" mode="tags" placeholder="请选择签到方式">
                <a-select-option value="1">点击签到按钮签到</a-select-option>
                <a-select-option value="2">扫码签到</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="打开小程序自动签到" name="isOpenCheckIn">
              <a-select ref="select" v-model:value="signInRules.isOpenCheckIn" placeholder="请选择小程序是否自动签到">
                <a-select-option :value="1">是</a-select-option>
                <a-select-option :value="0">否</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="签到二维码" name="checkInCodeUrl">
              <a-select ref="select" v-model:value="signInRules.checkInCodeUrl" placeholder="请选择小程序是否展示二维码">
                <a-select-option value="小程序首页显示">小程序首页显示</a-select-option>
                <a-select-option value="小程序首页不显示">小程序首页不显示</a-select-option>
              </a-select>
              <template> 如需专人检查入场凭证，建议开启 </template>
            </a-form-item>
            <a-form-item label="签到负责人" name="checkInHandleNickName">
              <a-input v-model:value="signInRules.checkInHandleNickName" placeholder="请填写签到负责人"></a-input>
            </a-form-item>
            <a-form-item label="签到通知" name="isCheckInNotice">
              <a-select ref="select" v-model:value="signInRules.isCheckInNotice" placeholder="请选择是否开启签到通知">
                <a-select-option :value="1">开启</a-select-option>
                <a-select-option :value="0">关闭</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="签到二维码背景图" name="checkInCodeBgUrl">
              <a-select ref="select" v-model:value="signInRules.checkInCodeBgUrl" placeholder="请选择二维码背景图设置">
                <a-select-option value="内置模板">内置模板</a-select-option>
                <a-select-option value="自定义">自定义</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="有效签到范围" name="checkInRange">
              <a-select v-model:value="signInRules.checkInRange" :options="Rangeoptions"
                placeholder="请选择有效签到范围"></a-select>
            </a-form-item>
            <a-form-item label="签到位置：">
              <a-button :type="mapShow == index ? 'primary' : 'default'"
                v-for="(value, index) in signInRules?.checkList" :key="index" style="margin-right: 5px;"
                @click="changeMap(value, index)">{{ value.place }}</a-button>
            </a-form-item>
            <a-form-item label="位置地图：">
              <div class="map-container">
                <signInMap ref="mapRef" @locationUpdate="handleLocationUpdate" :SignInArea="signInRules?.checkInRange">
                </signInMap>
              </div>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
* {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
}

ul,
p {
  margin-bottom: 0;
}

ul li {
  list-style: none;
}

.container {
  width: 100%;
  min-height: 100vh;
  padding-bottom: 20px;
  background: #f6f7f9;
}

.content {
  width: 1280px;
  height: auto;
  margin: 0 auto;
  padding-top: 54px;

  .main {
    width: 100%;
    background-color: #fff;
    padding: 18px 24px;

    .main-top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 24px;
    }

    .top-left {
      h3 {
        height: 100%;
        line-height: 40px;
        margin-bottom: 0;
      }
    }
  }

  .row-top {
    margin-top: 40px;
  }
}

.content-top {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #86909c;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding: 16px 0px;
}

.location-info {
  padding: 8px 12px;
  background: #f0f8ff;
  border-left: 4px solid #1890ff;
  border-radius: 4px;
  margin-bottom: 8px;

  .location-label {
    font-size: 16px;
    margin-right: 8px;
  }

  .location-name {
    font-weight: bold;
    color: #333;
    margin-right: 8px;
  }

  .location-address {
    color: #666;
    font-size: 14px;
  }
}

.map-container {
  width: 100%;
  height: 400px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

// 统计标签页样式
.statistics-container {
  margin-bottom: 24px;

  .stat-tabs {
    display: flex;
    gap: 0;
    background: #f5f5f5;
    border-radius: 6px;
    padding: 4px;
    width: fit-content;

    .stat-tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 12px 24px;
      min-width: 100px;
      background: transparent;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;

      .tab-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 4px;
        font-weight: 400;
      }

      .tab-number {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.8);
      }

      &.active {
        background: #1890ff;
        color: white;

        .tab-label {
          color: white;
        }

        .tab-number {
          color: white;
        }
      }
    }
  }
}

// 操作按钮样式
.action-buttons {
  margin-bottom: 16px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

// 表格容器样式
.table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;

  :deep(.ant-table) {
    .ant-table-thead>tr>th {
      background: #fafafa;
      font-weight: 600;
      color: #333;
    }

    .ant-table-tbody>tr:hover>td {
      background: #f5f8ff;
    }

    .ant-table-tbody>tr>td {
      border-bottom: 1px solid #f0f0f0;
    }
  }

  :deep(.ant-pagination) {
    margin: 16px 0;
    text-align: right;
  }
}

// 表格合计信息样式
.table-summary {
  padding: 12px 16px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
  text-align: left;

  .summary-text {
    font-size: 14px;
    color: #333;
    font-weight: 500;

    .summary-number {
      font-weight: 600;
      font-size: 16px;

      &.checked {
        color: #52c41a;
      }

      &.unchecked {
        color: #ff4d4f;
      }
    }
  }
}
</style>
