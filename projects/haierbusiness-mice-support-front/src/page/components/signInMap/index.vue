<script setup>
import { onMounted, onUnmounted, defineEmits, watch } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'

let map = null
let isMapLoaded = false
const props = defineProps({
  SignInArea: {
    type: Number,
    default: 1.5,
  },
})

// 定义事件发射器
const emit = defineEmits(['locationUpdate'])

onMounted(() => {
  // 设置高德地图安全配置
  window._AMapSecurityConfig = {
    securityJsCode: '610db7cc7881574494e34fd00b13ab97',
  }
})

const createByMap = (lon, lat) => {
  // 1. 检查坐标值有效性
  if (isNaN(lon) || isNaN(lat)) {
    return;
  }
  AMapLoader.load({
    key: '25569e43d6c6bcfa4d39a1b920d8d2d1', // 申请好的Web端开发者Key，首次调用 load 时必填
    version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: ['AMap.Scale', 'AMap.PlaceSearch', 'AMap.Geocoder'], // 需要使用的插件列表
  })
    .then((AMap) => {
      const mapCenter = [lon, lat]
      console.log('高德地图API加载成功')
      isMapLoaded = true
      AMapInstance = AMap // 保存AMap实例引用

      map = new AMap.Map('map', {
        // 设置地图容器id
        viewMode: '3D', // 是否为3D地图模式
        zoom: 15, // 初始化地图级别，调整为更合适的级别
        center: [lon, lat], // 初始化地图中心点位置
      })

      // 绘制签到范围圆形
      const circle = new AMap.Circle({
        center: mapCenter,
        radius: Number(Number(props.SignInArea) * 1000),
        strokeColor: '#1791fc',
        strokeOpacity: 0.4,
        strokeWeight: 2,
        fillColor: '#1791fc',
        fillOpacity: 0.4
      })
      map.add(circle)

      // 添加标记点
      addMarker(lon, lat)

      // 实时更新范围
      watch(props.SignInArea, (newVal) => {
        circle.setRadius(newVal)
      })

      // 地理编码，获取地址信息
      const geocoder = new AMap.Geocoder({
        city: '全国', // 城市设为全国
      })

      geocoder.getAddress([lon, lat], (status, result) => {
        if (status === 'complete' && result.regeocode) {
          const addressComponent = result.regeocode.addressComponent
          const formattedAddress = result.regeocode.formattedAddress

          // 发射位置更新事件
          emit('locationUpdate', {
            address: formattedAddress,
            name: addressComponent.building || addressComponent.township || '未知位置',
            city: addressComponent.city,
            district: addressComponent.district,
          })
        }
      })

      // 搜索附近POI
      AMap.plugin('AMap.PlaceSearch', function () {
        const placeSearch = new AMap.PlaceSearch({
          city: '',
          pageSize: 50,
          pageIndex: 1,
        })
        const cpoint = [lon, lat]
        placeSearch.searchNearBy(null, cpoint, 200, function (status, result) {
          if (status === 'complete' && result.poiList) {
            console.log('附近POI搜索结果:', result.poiList.pois)
          }
        })
      })
    })
    .catch((e) => {
      console.error('高德地图加载失败:', e)
    })
}

let AMapInstance = null // 保存AMap实例的引用

const addMarker = (lon, lat) => {
  if (!AMapInstance || !map) {
    console.warn('地图或AMap实例未初始化')
    return
  }
  // 1. 检查坐标值有效性
  if (isNaN(lon) || isNaN(lat)) {
    return;
  }
  // 创建一个 Marker 实例：
  const marker = new AMapInstance.Marker({
    position: new AMapInstance.LngLat(lon, lat), // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
  })
  // 将创建的点标记添加到已有的地图实例：
  map.add(marker)
}

const setZoomAndCenter = (lon, lat) => {
  if (map) {
    map.setZoomAndCenter(14, [lon, lat])
  }
}

defineExpose({
  addMarker,
  createByMap,
  setZoomAndCenter,
})
onUnmounted(() => {
  map?.destroy()
})
</script>

<template>
  <div class="mapContentBox">
    <div id="map"></div>
  </div>
</template>

<style lang="less" scoped>
.mapContentBox {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;

  .mapRightInfo {
    flex: 1;
    height: 100%;
    margin-left: 10px;
  }
}

#map {
  width: 100%;
  height: 100%;
}
</style>
