<!-- 会中首页 -->
<script lang="ts" setup>
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { meetingAttendeeApi } from '@haierbusiness-front/apis'
import { IMeetingDetails } from '@haierbusiness-front/common-libs'
const route = useRoute();
const router = useRouter();
//会议详情参数
const meetingDetails = ref<IMeetingDetails>({});
const miceCode = route.query.mainCode
const meetingId = ref()

//获取会议详情
const meetingDetailGet = async () => {
  if (miceCode) {
    const response = await meetingAttendeeApi.jumpDetails(miceCode as string);
    meetingDetails.value = response;
  } else {
    const response = await meetingAttendeeApi.details(1);
    if (response) {
      meetingDetails.value = response;
      meetingId.value = response.id
    }
  }
};

//跳转相应页面(简单处理)
const handleClick = (res: number) => {
  switch (res) {
    case 1:
      router.push({
        path: '/support/meeting/edit',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 2:
      router.push({
        path: '/support/attendeeMeeting/index',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 3:
      router.push({
        path: '/support/meeting/agenda',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 4:
      router.push({
        path: '/support/meeting/guest',
        query: {
          id: meetingDetails?.value.id,
        },
      });
      break;
    case 5:
      // 代码块2
      break;
    case 6:
      router.push({
        path: '/support/signIn/index',
        query: {
          miceInfoId: meetingDetails?.value.id,
          miceInfoName: meetingDetails?.value.miceName,
        },
      });
      break;
  }
};

//时间显示
const formatDateShow = (startDate?: string, endDate?: string): string => {
  if (!startDate || !endDate) return '';

  const format = (dateStr: string) => {
    const [year, month, day] = dateStr.split('-');
    return `${year}/${month}/${day}`;
  };

  try {
    return `${format(startDate)}~${format(endDate)}`;
  } catch {
    console.error('日期格式错误，应为YYYY-MM-DD格式');
    return `${startDate}~${endDate}`; // 保持原格式
  }
};

//会议来源枚举
const meetingScoure = (res: number | undefined | null) => {
  let ScoureName = '';
  if (res) {
    ScoureName = res == 1 ? '会务导入' : '会中创建';
  }
  return ScoureName;
};

// 存储清理函数的ref
const cleanupRef = ref<(() => void) | null>(null);

// 缩放处理函数
const handleResize = () => {
  const demandSubmit = document.getElementById('demand-submit');
  const bottomJump = document.getElementById('bottom-jump');
  if (!demandSubmit || !bottomJump) return;

  if (window.innerWidth <= 1099) {
    demandSubmit.style.transform = 'scale(0.5)';
    bottomJump.style.transform = 'scale(0.5)';
  } else if (window.innerWidth <= 1280) {
    demandSubmit.style.transform = 'scale(0.6)';
    bottomJump.style.transform = 'scale(0.6)';
    bottomJump.style.bottom = '20px';
  } else if (window.innerWidth <= 1536) {
    demandSubmit.style.transform = 'scale(0.8)';
    bottomJump.style.transform = 'scale(0.8)';
    demandSubmit.style.marginTop = '-32px';
    bottomJump.style.bottom = '40px';
  } else if (window.innerWidth <= 1720) {
    demandSubmit.style.transform = 'scale(0.9)';
    bottomJump.style.transform = 'scale(0.9)';
    demandSubmit.style.marginTop = '-16px';
    bottomJump.style.bottom = '54px';
  } else {
    demandSubmit.style.transform = 'scale(1)';
    bottomJump.style.transform = 'scale(1)';
    demandSubmit.style.marginTop = '0px';
    bottomJump.style.bottom = '74px';
  }
};

// 初始化缩放监听器
const initResizeListener = () => {
  window.addEventListener('resize', handleResize);
  return () => window.removeEventListener('resize', handleResize);
};

//处理会议顾问/经办人显示
const counsellorShow = (nameList: string[] | undefined | string, codeList: string[] | undefined | string) => {
  if (!nameList || !codeList) return []
  const nickName = Array.isArray(nameList)
    ? nameList
    : nameList?.split(',') || []

  const userName = Array.isArray(codeList)
    ? codeList
    : codeList?.split(',') || []

  const showList = nickName.map((item, index) => ({
    name: item || '',
    code: userName[index] || '',
  }))

  return showList.map(item => `${item.name}(${item.code})`)
}


onMounted(() => {

  // 初始化缩放监听器
  cleanupRef.value = initResizeListener();

  // 监听窗口大小变化
  watch(
    () => window.innerWidth,
    () => {
      // 清理之前的监听器
      if (cleanupRef.value) {
        cleanupRef.value();
      }
      // 重新设置监听器
      const newCleanup = initResizeListener();
      // 更新清理函数引用
      cleanupRef.value = newCleanup;
    },
  );
  handleResize()

  //获取会议详情
  meetingDetailGet();
});

//完成会议
const completeMeeting = (res:number)=>{
  meetingAttendeeApi.meetingState(meetingId.value,res)
}

// 组件卸载时清理监听器
onUnmounted(() => {
  if (cleanupRef.value) {
    cleanupRef.value();
  }
});
</script>
<template>
  <div class="container">
    <div class="content">
      <div class="main" id="demand-submit">
        <div class="main-top">
          <div class="top-left">
            <h3>{{ meetingDetails?.miceName }}</h3>
          </div>
          <div class="top-right">
            <a-button style="margin-right: 10px" @click="completeMeeting(2)">取消会议</a-button>
            <a-button type="primary" @click="completeMeeting(1)">完成会议</a-button>
          </div>
        </div>
        <div class="main-middle">
          <a-row :gutter="[16, 16]">
            <a-col :span="8">
              <span class="foot-color">会议名称：</span>{{ meetingDetails?.miceName }}
            </a-col>
            <a-col :span="6"><span class="foot-color">会议来源：</span>{{ meetingScoure(meetingDetails?.miceSource)
            }}</a-col>
            <a-col :span="10" class="flex">
              <span class="foot-color">会议时间：</span>
              <div>{{ formatDateShow(meetingDetails?.miceStartDate, meetingDetails?.miceEndDate) }}</div>
            </a-col>
          </a-row>
          <a-row :gutter="[16, 16]" class="row-top">
            <a-col :span="8"><span class="foot-color">关联会议：</span>{{ meetingDetails?.miceConnectMeetingId }}</a-col>
            <a-col :span="16" class="hiddle">
              <span class="foot-color">会议酒店：</span>
              <a-tooltip>
                <template #title>{{ meetingDetails?.miceHotelName?.split(',').join('，') }}</template>
                {{ meetingDetails?.miceHotelName?.split(',').join('，') }}
              </a-tooltip>
            </a-col>
          </a-row>
          <a-row :gutter="[16, 16]" class="row-top">
            <a-col :span="8" class="hiddle">
              <span class="foot-color">经办人：</span>
              <a-tooltip>
                <template #title>{{
                  counsellorShow(meetingDetails?.operatorName, meetingDetails?.operatorCode).join('，') }}</template> 
                {{
                  counsellorShow(meetingDetails?.operatorName, meetingDetails?.operatorCode).join('，') }}
              </a-tooltip>

            </a-col>
            <a-col :span="16" class="hiddle">
              <span class="foot-color">会议顾问：</span>
              <a-tooltip>
                <template #title>{{
                  counsellorShow(meetingDetails?.consultantUsername, meetingDetails?.consultantUserCode).join('，')
                  }}</template>
                {{
                  counsellorShow(meetingDetails?.consultantUsername, meetingDetails?.consultantUserCode).join('，') }}
              </a-tooltip>

            </a-col>
          </a-row>
          <!-- <a-row :gutter="[16, 16]" class="row-top">
            <a-col :span="4" style="text-align: right;">会议二维码：</a-col>
            <a-col :span="7" style="color: blue;">点击查看</a-col>
          </a-row> -->
        </div>
        <div class="main-bottom">
          <div class=" edit-size btn-style" @click="handleClick(1)">
            编辑
          </div>
          <div class="attendee-size btn-style" @click="handleClick(2)">
            参会人管理
          </div>
          <div class="agenda-size btn-style" @click="handleClick(3)">
            议程管理
          </div>
          <div class="contact-size btn-style">
            现场联系人
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-jump" id="bottom-jump">
      <div class="jump-box" @click="handleClick(6)">
        <img src="../../assets/image/meeting/signIn.png" alt="">
        <p>签到管理</p>
      </div>
      <div class="jump-box">
        <img src="../../assets/image/meeting/seatingArrangement.png" alt="">
        <p>座次管理</p>
      </div>
      <div class="jump-box">
        <img src="../../assets/image/meeting/reception.png" alt="">
        <p>全流程接待</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/evaluation.png" alt="">
        <p>评价</p>
      </div>
      <div class="jump-box" @click="handleClick(4)">
        <img src="@/assets/image/meeting/guest.png" alt="">
        <p>嘉宾管理</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/seatSign.png" alt="">
        <p>座位牌打印</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/nameTag.png" alt="">
        <p>胸牌打印</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/Marketing.png" alt="">
        <p>营销微站</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/AI.png" alt="">
        <p>AL助手</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/notice.png" alt="">
        <p>通知管理</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/lotteryDraw.png" alt="">
        <p>抽奖</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/questions.png" alt="">
        <p>现场问答</p>
      </div>
      <div class="jump-box">
        <img src="@/assets/image/meeting/vote.png" alt="">
        <p>投票</p>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
* {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
}

ul,
p {
  margin-bottom: 0;
}

ul li {
  list-style: none;
}

.container {
  width: 100%;
  background: url(@/assets/image/meeting/banner.png) no-repeat;
  background-size: 1920px 240px;
  background-position: center top;
}

.content {
  width: 1280px;
  height: calc(100vh - 78px);
  margin: 0 auto;
  padding-top: 130px;

  .main {
    width: 100%;
    height: 276px;
    background-color: #fff;
    border-radius: 12px;
    padding: 18px 24px;

    .main-top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 24px;
    }

    .row-top {
      margin-top: 16px;
    }

    .main-middle {
      width: 1232px;
      height: 116px;
      background: #F6F9FC;
      border-radius: 8px;
      padding: 12px;
      color: #1D2129;
    }

    .main-bottom {
      display: flex;
      margin-top: 24px;
      justify-content: left;
      grid-gap: 15px;

      img {
        width: 14px;
        height: 14px;
        margin-right: 3px;
      }

      .btn-style {
        height: 32px;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        text-align: center;
        line-height: 32px;
        color: #1D2129;
        cursor: pointer;
        font-size: 14px;
        padding-left: 19px;
        background-color: rgba(255, 255, 255, 0);
        background-position: 15px 9px;
        background-repeat: no-repeat;
        background-size: 14px;
        transition: 0.2s ease;

        &:hover {
          border-color: #1868DB;
          color: #1868DB;
        }
      }

      .edit-size {
        width: 78px;
        color: #fff;
        border: none;
        background-color: #1868DB;
        background-image: url(@/assets/image/meeting/edit.png);
        background-position: 15px 9px;

        &:hover {
          background-color: #3984ed;
          color: #fff;
        }
      }

      .attendee-size {
        width: 120px;
        background-image: url(@/assets/image/meeting/TeamFilled.png);
        &:hover{
          background-image: url(@/assets/image/meeting/TeamFilledBlue.png);
          background-position: 15px 9px;
        }
      }

      .agenda-size {
        width: 106px;
        background-image: url(@/assets/image/meeting/MeetingRoomFille.png);
        &:hover{
          background-image: url(@/assets/image/meeting/MeetingRoomFilledBlue.png);
          background-position: 15px 9px;
        }
      }

      .contact-size {
        width: 134px;
        background-image: url(@/assets/image/meeting/TelephoneFilled.png);
        &:hover{
          background-image: url(@/assets/image/meeting/TelephoneFilledBlue.png);
          background-position: 15px 9px;
        }
      }
    }
  }
}

.foot-color {
  color: #86909C;
}

.ant-btn {
  border-radius: 4px;
}

.top-left {
  h3 {
    height: 100%;
    line-height: 40px;
    margin-bottom: 0;
  }
}

.bottom-jump {
  width: 1612px;
  height: 138px;
  margin: auto;
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0px 3px 8px 0px rgba(1, 12, 51, 0.07);
  border-radius: 36px;
  border: 1px solid rgba(24, 104, 219, 0.1);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: 72px;
  left: 50%;
  margin-left: -806px;
}

.jump-box {
  max-width: 70px;
  height: 90px;
  text-align: center;
  margin-right: 60px;
  cursor: pointer;

  &:last-child {
    margin-right: 0;
  }

  img {
    width: 60px;
    height: 60px;
    transition:
      transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 1.5),
      filter 0.3s ease;
    transform-style: preserve-3d;
    transform-origin: center center;
    filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
  }

  img:hover {
    transform:
      perspective(1000px) translateZ(60px) translateY(-15px) scale(1.4) rotateX(10deg) rotateY(5deg);
    filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.3));
  }


  p {
    font-size: 14px;
    margin-top: 10px;
    color: #1D2129;
  }
}

.hiddle {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
