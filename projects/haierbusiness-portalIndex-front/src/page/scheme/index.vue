<script lang="ts" setup>
// 标的方案 - 用户端
import Scheme from '@haierbusiness-front/components/mice/scheme/index.vue'
import SchemeView from '@haierbusiness-front/components/mice/scheme/view.vue'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const isView = ref(false) // 是否为视图

onMounted(() => {
  isView.value = route.path === '/bidman/scheme/confirm/view'
})
</script>

<template>
  <div>
    <SchemeView v-if="isView" orderSource="user"></SchemeView>
    <Scheme v-else orderSource="user"></Scheme>
  </div>
</template>
