<script lang="ts" setup>
import paymentDetails from '@haierbusiness-front/components/mice/payment/detailView/index.vue'
import { useRoute } from 'vue-router'
import { resolveParam } from '@haierbusiness-front/utils'
import { computed } from 'vue'

const route = useRoute()

// 获取路由参数
const record = computed(() => {
  if (route.query.record) {
    return resolveParam(route.query.record as string)
  }
  return null
})

// 获取类型参数，默认为 payment
const type = computed(() => {
  return (route.query.type as string) || 'payment'
})

// 获取模式参数，默认为 edit
const mode = computed(() => {
  return (route.query.mode as string) || 'edit'
})
</script>
<template>
  <div>
    <paymentDetails
      :type="type"
      :record="record"
      :mode="mode"
    />
  </div>
</template>
<style lang="less" scoped></style>
