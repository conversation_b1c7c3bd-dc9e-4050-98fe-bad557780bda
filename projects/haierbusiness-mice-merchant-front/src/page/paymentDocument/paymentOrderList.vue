<!-- 付款单列表 -->
<script lang="ts" setup>
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Tag as hTag,
  RangePicker as hRangePicker,
  Input as hInput,
  message,
  TableProps,
  Tooltip,
  Popover as hPopover,
} from 'ant-design-vue'
import { ColumnType } from 'ant-design-vue/lib/table/interface'
import { Key } from 'ant-design-vue/lib/vc-table/interface'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { paymentFromApi } from '@haierbusiness-front/apis'
import { IPaymentFromFilter, IPaymentFrom } from '@haierbusiness-front/common-libs'
import dayjs, { Dayjs } from 'dayjs'
import { computed, ref, watch, onMounted } from 'vue'
import { DataType, usePagination } from 'vue-request'
import { resolveParam, routerParam } from '@haierbusiness-front/utils'
import router from '../../router'
import { InvoiceStatusEnum, InvoiceStatusMap, InvoiceStatusTagColorMap } from '@haierbusiness-front/common-libs'
import type { MenuInfo } from 'ant-design-vue/lib/menu/src/interface'

const currentRouter = ref()

onMounted(async () => {
  currentRouter.value = await router
  // 页面初始化时调用列表接口
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  })
})

const columns: ColumnType[] = [
  {
    title: '付款单号',
    dataIndex: 'paymentCode',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单总金额',
    dataIndex: 'totalAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '结算比例',
    dataIndex: 'settlementRate',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '130px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '200px',
    fixed: 'right',
    align: 'center',
  },
]
const searchParam = ref<IPaymentFromFilter>({})

const { data, run: listApiRun, loading, current, pageSize } = usePagination(paymentFromApi.getMerchantPayMentList)

const reset = () => {
  searchParam.value = {}
  beginAndEnd.value = undefined
}

const dataSource = computed(() => data.value?.records || [])

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}))

const handleTableChange = (pag: { current: number; pageSize: number }, filters?: any, sorter?: any) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  })
}


//查看
const handleView = (record?: any) => {
  if (record && record.id) {
    // 跳转到详情页面，传递type参数
    const params = {
      id: record.id,
      paymentCode: record.paymentCode,
    }
    currentRouter.value.push({
      path: '/mice-merchant/paymentDocument/paymentDetails',
      query: {
        record: routerParam(params),
        type: 'paymentOrder', // 指定为付款单类型
        mode: 'view', // 指定为查看模式
      },
    })
  }
}

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(
  () => beginAndEnd.value,
  (n: any, o: any) => {
    if (n) {
      searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
      searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
    } else {
      searchParam.value.startTime = undefined
      searchParam.value.endTime = undefined
    }
  },
)


// 跳转到上传发票页面
const navigateToUploadInvoice = (record: any) => {
  currentRouter.value.push({
    path: '/mice-merchant/paymentDocument/paymentUploadInvoice',
    query: {
      record: encodeURIComponent(JSON.stringify(record)),
      type: 'payment', // 明确指定为付款场景
    },
  })
}


// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string
  switch (key) {
    case 'view':
      // 查看详情
      handleView(record)
      break
    case 'upload': // 跳转到付款凭证上传页面
    {
      const params = {
        id: record.id, // 统一使用id字段
        paymentCode: record.paymentCode,
      }
      currentRouter.value.push({
        path: '/mice-merchant/paymentDocument/paymentDetails',
        query: {
          record: routerParam(params),
          type: 'paymentOrder', // 添加type参数，与查看保持一致
          mode: 'edit', // 明确指定为编辑模式
        },
      })
      break
    }
    case 'uploadInvoice':
      // 跳转到上传发票页面
      navigateToUploadInvoice(record)
      break
    default:
      break
  }
}

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = []

  // 查看按钮始终显示
  options.push({
    key: 'view',
    label: '查看',
  })

  // 根据状态添加不同的操作选项
  if (record.status == InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD) {
    // 等待服务商上传发票：显示上传发票按钮
    options.push({
      key: 'uploadInvoice',
      label: '上传发票',
    })
  } else if (record.status == InvoiceStatusEnum.FINANCIAL_REJECTED) {
    // 财务驳回：显示重新上传发票按钮
    options.push({
      key: 'uploadInvoice',
      label: '重新上传发票',
    })
  }
  // 已完成状态只显示查看，不需要额外的菜单选项

  return options
}
</script>

<template>
  <div class="main-container">
    <h-row :align="'middle'">
      <h-col :span="24" class="margin-bottom-10">
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="3" style="text-align: right; padding-right: 10px">
            <label for="createTime">付款单创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" allow-clear style="width: 100%" value-format="YYYY-MM-DD" />
          </h-col>

          <h-col :span="2" style="text-align: right; padding-right: 10px">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.status" allow-clear placeholder="请选择状态" style="width: 100%">
              <h-select-option :value="InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD">
                {{ InvoiceStatusMap[InvoiceStatusEnum.PENDING_SERVICE_PROVIDER_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM">
                {{ InvoiceStatusMap[InvoiceStatusEnum.PENDING_FINANCIAL_CONFIRM] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.FINANCIAL_REJECTED">
                {{ InvoiceStatusMap[InvoiceStatusEnum.FINANCIAL_REJECTED] }}
              </h-select-option>
              <h-select-option :value="InvoiceStatusEnum.COMPLETED">
                {{ InvoiceStatusMap[InvoiceStatusEnum.COMPLETED] }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" style="padding: 10px 10px 0px 10px">
          <h-col :span="24" style="text-align: right">
            <h-button style="margin-right: 10px" @click="reset">重置</h-button>
            <h-button
              :disabled="loading"
              :loading="loading"
              type="primary"
              @click="handleTableChange({ current: 1, pageSize: 10 })"
            >
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          :row-key="(record) => record.id"
          :scroll="{ y: 550 }"
          :size="'small'"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'paymentCode'">
              <Tooltip :title="record.paymentCode">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.paymentCode }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'merchantName'">
              <Tooltip :title="record.merchantName">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.merchantName }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <h-tag
                :color="InvoiceStatusTagColorMap[record.status as keyof typeof InvoiceStatusTagColorMap] || 'default'"
              >
                {{ InvoiceStatusMap[record.status as keyof typeof InvoiceStatusMap] || '未知状态' }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <div class="operator-buttons">
                <h-popover
                  placement="bottom"
                  trigger="hover"
                  arrow-point-at-center
                >
                  <template #content>
                    <h-button
                      v-for="option in getMenuOptions(record)"
                      :key="option.key"
                      style="display: block; width: 100%; text-align: left"
                      type="link"
                      @click="handleMenuClick(record, { key: option.key })"
                    >
                      {{ option.label }}
                    </h-button>
                  </template>
                  <h-button type="link">操作</h-button>
                </h-popover>
              </div>
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>

  </div>
</template>

<style lang="less" scoped>
.main-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

.margin-bottom-10 {
  margin-bottom: 10px;
}

.padding-standard {
  padding: 10px 10px 0px 10px;
}

.text-right-padding {
  text-align: right;
  padding-right: 10px;
}

.width-full {
  width: 100%;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.margin-right-10 {
  margin-right: 10px;
}


.operator-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}


.operator-buttons :deep(.ant-btn) {
  padding: 0 4px;
  font-size: 14px;
}

</style>
