<script lang="ts" setup>
import { onBeforeMount, onBeforeUnmount, onMounted, onUnmounted, ref } from 'vue'
import EManage from '@haierbusiness-front/components/manange/EManange.vue'
import { storeToRefs } from 'pinia'
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton'
//通知弹框逻辑
import notice from './component/Modal.vue'

const store = applicationStore()
const { resource } = storeToRefs(store)
//通知弹框逻辑
const noticeVisible = ref(false)
//
const handleShow = () => {
  noticeVisible.value = false
  console.log(noticeVisible.value)
}
const handlecancel = () => {
  noticeVisible.value = false
}

onMounted(() => {
  // 通知弹框
  const showValue = sessionStorage.getItem('noticeModelShow')
  if (showValue === null) {
    noticeVisible.value = true
    sessionStorage.setItem('noticeModelShow', JSON.stringify(false))
  } else {
    noticeVisible.value = JSON.parse(showValue)
  }
})

// 根据实际需求决定是否保留这一行
onUnmounted(() => {
  sessionStorage.removeItem('noticeModelShow')
})
</script>

<template>
  <div style="height: 100vh; min-height: 280px">
    <e-manage :param="resource"></e-manage>
    <!-- 通知弹框 -->
    <notice v-model="noticeVisible" style="width: 500px" @cancel="handlecancel" @ok="handleShow"></notice>
  </div>
</template>

<style lang="less" scoped>
:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}
</style>
