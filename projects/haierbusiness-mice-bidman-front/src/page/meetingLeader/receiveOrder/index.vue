<script lang="ts" setup>
// 接收订单 - 管理端
import { reactive, ref } from 'vue'
import { defaultSearchValues, useReceiveOrderStore } from './store'
import { Form, Modal, TableProps } from 'ant-design-vue'
import { MiceBidManNotice } from '@haierbusiness-front/common-libs'
import { ColumnType } from 'ant-design-vue/es/table'
import MeetingConsultantDrawer from '@haierbusiness-front/components/meetingConsultantDrawer/index.vue'
import ConsultantImg from '@/assets/image/demo/consultant.png'

// 表格列配置
const columns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'order',
    key: 'order',
  },
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title',
    width: '150px',
    ellipsis: true,
  },
  {
    title: '内容形式',
    dataIndex: 'contentForm',
    key: 'contentForm',
  },
  {
    title: '通知作用范围',
    dataIndex: 'effectScope',
    key: 'effectScope',
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
  },
  {
    title: '状态',
    dataIndex: 'state',
    key: 'state',
  },
  {
    title: '是否弹窗通知',
    dataIndex: 'isWindow',
    key: 'isWindow',
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    key: 'createName',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    key: 'gmtCreate',
  },
  {
    title: '操作',
    dataIndex: 'options',
    key: 'options',
    width: '100px',
    fixed: 'right',
  },
]

const searchData = [
  {
    name: '沈居丽',
    desc: '10年行业专家顾问',
    isRecommend: true,
    avatar: ConsultantImg,
    performance: [
      { label: '2025年承担总量', value: '32单 343,210.50元' },
      {
        label: '正在进行中',
        value: '2单 3,210.00元',
      },
    ],
    statistics: [
      { label: '需求互动', value: '2' },
      { label: '需求发布', value: '6' },
      { label: '方案审核', value: '2' },
      { label: '竞价推送', value: '1' },
      { label: '账单审核', value: '25' },
    ],
  },
  {
    name: '唐浩',
    desc: '8年资深顾问',
    isRecommend: false,
    avatar: ConsultantImg,
    performance: [
      { label: '2025年承担总量', value: '32单 343,210.50元' },
      {
        label: '正在进行中',
        value: '2单 3,210.00元',
      },
    ],
    statistics: [
      { label: '需求互动', value: '5' },
      { label: '需求发布', value: '12' },
      { label: '方案审核', value: '7' },
      { label: '竞价推送', value: '3' },
      { label: '账单审核', value: '12' },
    ],
  },
]

// 变化的数据
const store = useReceiveOrderStore()
const useForm = Form.useForm
const originSearchValues = reactive({ ...defaultSearchValues })
const searchValues = reactive({ ...defaultSearchValues })
const pagination = reactive({ current: 1, pageSize: 10 })
const editModalOpen = ref(false)
const { resetFields } = useForm(searchValues)

// 获取公告通知列表
const getNoticeList = () => {
  store.getNoticeList({ pageNum: pagination.current, pageSize: pagination.pageSize, ...originSearchValues })
}

// 查询
const handleSearch = () => {
  originSearchValues.title = searchValues.title
  originSearchValues.effectScope = searchValues.effectScope
  originSearchValues.state = searchValues.state
  originSearchValues.createName = searchValues.createName
  pagination.current = 1
  pagination.pageSize = 10
}

// 重置
const handleReset = () => {
  resetFields()
  handleSearch()
}

// 编辑
const handleEdit = (record: MiceBidManNotice) => {
  store.noticeDetail.id = record.id
  store.noticeDetail.title = record.title
  store.noticeDetail.effectScope = record.effectScope
  store.noticeDetail.contentForm = record.contentForm
  store.noticeDetail.informContent = record.informContent
  store.noticeDetail.sort = record.sort
  store.noticeDetail.state = record.state
  store.noticeDetail.isWindow = record.isWindow
  editModalOpen.value = true
}

// 删除
const handleDelete = (record: MiceBidManNotice) => {
  Modal.confirm({
    title: '是否确定删除?',
    async onOk() {
      await store.deleteNotice({ id: record.id! })
      store.getNoticeList({ pageNum: pagination.current, pageSize: pagination.pageSize, ...originSearchValues })
    },
  })
}

// 表格分页
const handleTableChange: TableProps['onChange'] = ({ pageSize, current }) => {
  current && (pagination.current = current)
  pageSize && (pagination.pageSize = pageSize)
}
</script>

<template>
  <!-- 公告通知列表页 -->
  <div class="notice-list">
    <a-form autocomplete="off" class="search">
      <a-row :gutter="10">
        <a-col :span="6">
          <a-form-item label="通知标题" name="title">
            <a-input v-model:value="searchValues.title" />
          </a-form-item>
        </a-col>
        <!-- <a-col :span="6">
          <a-form-item
            label="场景"
            name="contentForm"
          >
            <a-select v-model:value="searchValues.contentForm" allow-clear :options="store.effectScopeOptions" />
          </a-form-item>
        </a-col> -->
        <a-col :span="6">
          <a-form-item label="通知作用范围" name="effectScope">
            <a-select v-model:value="searchValues.effectScope" :options="store.effectScopeOptions" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="状态" name="state">
            <a-select v-model:value="searchValues.state" :options="store.stateOptions" allow-clear />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="创建人" name="createName">
            <a-input v-model:value="searchValues.createName" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-space class="search-buttons">
        <a-button type="primary" @click="handleSearch">查询</a-button>
        <a-button @click="handleReset">重置</a-button>
      </a-space>
    </a-form>
    <div class="toolbar">
      <a-button type="primary" @click="editModalOpen = true">新增</a-button>
    </div>
    <a-table
      :columns="columns"
      :dataSource="store.noticeList"
      :pagination="{ ...pagination, total: store.noticeListTotal }"
      :scroll="{ x: true, y: 540 }"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'effectScope'">
          {{ store.effectScopeOptions.find((item) => item.value === record[column.key])?.label }}
        </template>
        <template v-if="column.key === 'state'">
          {{ store.stateOptions.find((item) => item.value === record[column.key])?.label }}
        </template>
        <a-space v-if="column.key === 'options'">
          <a @click="handleEdit(record)">编辑</a>
          <a @click="handleDelete(record)">删除</a>
        </a-space>
      </template>
    </a-table>
  </div>
  <!-- <edit-modal v-model="editModalOpen" :getDataList="getNoticeList"></edit-modal> -->
  <meeting-consultant-drawer
    v-model="editModalOpen"
    :items="searchData"
    title="会务顾问"
    @assign="console.log"
    @search="console.log"
  />
</template>

<style lang="less" scoped>
.notice-list {
  background-color: #fff;
  padding: 10px;

  .search {
    margin-bottom: 10px;

    .search-buttons {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }

  .toolbar {
    margin: 10px 0;
  }
}
</style>
