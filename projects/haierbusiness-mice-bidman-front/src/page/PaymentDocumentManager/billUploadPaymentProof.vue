<!-- 上传付款凭证页面 -->
<script lang="ts" setup>
import {
  Button as hButton,
  Card as hCard,
  message,
  Spin,
  Table as hTable,
  TabPane as ATabPane,
  Tabs as ATabs,
  Textarea as ATextarea,
  Upload as hUpload,
} from 'ant-design-vue'
import { ColumnType } from 'ant-design-vue/lib/table/interface'
import { UploadOutlined } from '@ant-design/icons-vue'
import { fileApi, paymentFromApi } from '@haierbusiness-front/apis'
import type { Ref } from 'vue'
import { computed, inject, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { resolveParam, getFileNameFromPath } from '@haierbusiness-front/utils'
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton'
import { InvoiceStatusMap, InvoiceStatusEnum } from '@haierbusiness-front/common-libs'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const store = applicationStore()

// 获取路由参数
const record = resolveParam(route.query.record as string)
const hideBtn = record?.hideBtn || ''
const mode = (route.query.mode as string) || 'upload' // 默认为上传模式，兼容原有调用

// 获取并设置frameModel
const frameModel = inject<any>('frameModel')
if (frameModel) {
  frameModel.value = hideBtn === '1' ? 1 : 0
}

// 获取关闭标签页控制器
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab')

const paymentId = computed(() => record?.id || (route.query.id as string))

// 模式相关计算属性
const isViewMode = computed(() => mode === 'view')
const isUploadMode = computed(() => mode === 'upload')
const pageTitle = computed(() => (isViewMode.value ? '付款单详情' : '上传付款凭证'))

// 权限判断：检查是否为会务顾问（会务顾问不能上传付款凭证）
const isConsultant = computed(() => {
  if (!store.loginUser?.authorities) return false
  return store.loginUser.authorities.some((item) => item.authority === '211')
})

// 页面状态
const loading = ref(false)
const uploadLoading = ref(false)
const currentDetailRecord = ref<any>(null)
const activeKey = ref('1')
const fileList = ref<any[]>([])
const ReasonsRejection = ref('')
const baseUrl = import.meta.env.VITE_BUSINESS_URL

// 详情页订单表格列
const detailOrderColumns: ColumnType[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
  },
  {
    title: '会议时间',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`
      }
      return ''
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text.toFixed(2)}元` : ''),
  },
  {
    title: '付款比例',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text.toFixed(2)}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text.toFixed(2)}元` : ''),
  },
]

// 详情页发票表格列
const detailInvoiceColumns: ColumnType[] = [
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    align: 'center',
  },
  {
    title: '发票日期',
    dataIndex: 'invoiceDate',
    align: 'center',
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text.toFixed(2)}元` : ''),
  },
]

// 获取付款单详情
const getPaymentDetail = async () => {
  if (!paymentId.value) {
    message.error('付款单ID不存在')
    return
  }

  loading.value = true
  try {
    const res = await paymentFromApi.getDetails(paymentId.value)
    currentDetailRecord.value = res
  } catch (error) {
    console.error('获取详情失败:', error)
    message.error('获取付款单详情失败')
  } finally {
    loading.value = false
  }
}

// 计算发票金额合计
const calculateInvoiceTotal = () => {
  if (!currentDetailRecord.value) return '0.00'
  const invoiceData = getInvoiceData()
  const total = invoiceData.reduce((sum: number, item: any) => sum + (item.invoiceAmount || 0), 0)
  return `${total.toFixed(2)}元`
}

// 获取发票数据
const getInvoiceData = () => {
  if (!currentDetailRecord.value || !currentDetailRecord.value.miceInvoiceQueryDetails) {
    return []
  }

  return currentDetailRecord.value.miceInvoiceQueryDetails.map((invoice: any, index: number) => ({
    key: invoice.id || index,
    invoiceNumber: invoice.invoiceNumber || '',
    invoiceDate: invoice.invoiceDate || '',
    invoiceAmount: invoice.invoiceAmount || 0,
  }))
}

// 获取订单数据
const getOrderData = () => {
  if (!currentDetailRecord.value || !currentDetailRecord.value.paymentRecordsDetails) {
    return []
  }

  return currentDetailRecord.value.paymentRecordsDetails.map((record: any, index: number) => ({
    key: record.id || index,
    mainCode: record.mainCode || '',
    startDate: record.startDate || '',
    endDate: record.endDate || '',
    operatorName: record.operatorName || '',
    billAmount: record.billAmount || 0,
    feeRate: record.feeRate || 0,
    paymentAmount: record.paymentAmount || 0,
  }))
}

// 文件上传处理
const uploadRequest = (options: any) => {
  uploadLoading.value = true

  const formData = new FormData()
  formData.append('file', options.file)

  console.log('开始上传文件:', options.file.name)

  fileApi
    .upload(formData)
    .then((it) => {
      options.file.filePath = baseUrl + it.path
      options.file.fileName = options.file.name
      options.onProgress(100)
      options.onSuccess(it, options.file)

      console.log('文件上传成功:', options.file)

      // 确保文件被添加到列表中
      if (!fileList.value.some((f) => f.fileName === options.file.name)) {
        fileList.value.push(options.file)
      }
    })
    .catch((error) => {
      console.error('上传失败:', error)
      message.error('文件上传失败，请重试')
      options.onError(error)
    })
    .finally(() => {
      uploadLoading.value = false
    })
}

// 文件删除处理
const handleFileRemove = (file: any) => {
  const index = fileList.value.findIndex((f) => f.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
}

// 提交上传的付款凭证
const submitUpload = () => {
  if (fileList.value.length === 0) {
    message.error('请先上传付款凭证')
    return
  }

  if (!currentDetailRecord.value?.id) {
    message.error('记录ID不存在')
    return
  }

  // 提取文件路径
  const attachmentFiles = fileList.value.map((file) => file.filePath).filter(Boolean) as string[]

  if (attachmentFiles.length === 0) {
    message.error('文件上传未完成，请重试')
    return
  }

  uploadLoading.value = true

  // 上传付款凭证
  paymentFromApi
    .confirmPayment({
      id: currentDetailRecord.value.id,
      attachmentFile: attachmentFiles,
    })
    .then(() => {
      message.success('付款凭证上传成功')
      // 设置关闭当前标签页标识
      if (isCloseLastTab) {
        isCloseLastTab.value = true
      }
      // 返回列表页面
      router.push('/bidman/PaymentDocumentManager/paymentFrom')
    })
    .catch((error) => {
      console.error('提交付款凭证失败:', error)
      message.error('提交付款凭证失败')
    })
    .finally(() => {
      uploadLoading.value = false
    })
}

// 驳回付款
const handleReject = () => {
  if (!currentDetailRecord.value?.id) {
    message.error('记录ID不存在')
    return
  }

  if (!ReasonsRejection.value.trim()) {
    message.error('请填写驳回原因')
    return
  }

  uploadLoading.value = true

  // 调用驳回付款接口
  paymentFromApi
    .rejectPayment({
      id: currentDetailRecord.value.id,
      remark: ReasonsRejection.value.trim(),
    })
    .then(() => {
      message.success('驳回成功')
      // 设置关闭当前标签页标识
      if (isCloseLastTab) {
        isCloseLastTab.value = true
      }
      // 返回列表页面
      router.push('/bidman/PaymentDocumentManager/paymentFrom')
    })
    .catch((error) => {
      console.error('驳回失败:', error)
      message.error('驳回失败')
    })
    .finally(() => {
      uploadLoading.value = false
    })
}

// 返回列表页面
const goBack = () => {
  // 设置关闭当前标签页标识
  if (isCloseLastTab) {
    isCloseLastTab.value = true
  }
  router.push('/bidman/PaymentDocumentManager/paymentFrom')
}

// 获取状态文本
const getStatusText = () => {
  if (!currentDetailRecord.value?.status) {
    return '未知状态'
  }

  const status = currentDetailRecord.value.status

  // 使用枚举映射获取状态文本
  return InvoiceStatusMap[status as keyof typeof InvoiceStatusMap] || '未知状态'
}

// 页面初始化
onMounted(() => {
  getPaymentDetail()
})
</script>

<template>
  <div class="container">
    <Spin :spinning="loading" style="height: 100%;">
      <h-card v-if="currentDetailRecord" class="detail-card">
        <!-- 基本信息 -->
        <div class="payment-info-card">
          <div class="payment-info-content">
            <!-- 主标题：服务商名称 -->
            <div class="payment-main-title">
              <div class="payment-main-icon"></div>
              <span class="payment-main-name">{{ currentDetailRecord.merchantName || '未知服务商' }}</span>
            </div>

            <!-- 付款单号 -->
            <div class="payment-code">
              <span>付款单号：{{ currentDetailRecord.paymentCode || '-' }}</span>
            </div>

            <!-- 详细信息 -->
            <div class="payment-info-details">
              <div class="payment-info-item">
                <span class="info-title amount-icon">付款总金额：</span>
                <span class="info-value">{{
                  currentDetailRecord.paymentAmount ? `${currentDetailRecord.paymentAmount.toFixed(2)}元` : '-'
                }}</span>
              </div>
              <div class="payment-info-item">
                <span class="info-title file-icon">创建时间：</span>
                <span class="info-value">{{ currentDetailRecord.gmtCreate || '-' }}</span>
              </div>
              <div class="payment-info-item">
                <span class="info-title status-icon">状态：</span>
                <span class="info-value">
                  {{ getStatusText() }}
                </span>
              </div>
              <div v-if="currentDetailRecord.remark && currentDetailRecord.status === InvoiceStatusEnum.FINANCIAL_REJECTED" class="payment-info-item">
                <span class="info-title remark-icon">驳回原因：</span>
                <span class="info-value">{{ currentDetailRecord.remark }}</span>
              </div>
            </div>

            <div
              v-if="currentDetailRecord.attachmentFiles && currentDetailRecord.attachmentFiles.length > 0"
              class="attachment-section"
            >
              <span class="info-title file-icon">已上传凭证：</span>
              <div class="attachment-files">
                <template v-for="(file, index) in currentDetailRecord.attachmentFiles" :key="index">
                  <a :href="file.path" class="file-link" target="_blank">
                    {{ getFileNameFromPath(file.path || `付款凭证${index + 1}`) }}
                  </a>
                </template>
              </div>
            </div>
          </div>
        </div>

        <!-- Tab页 -->
        <a-tabs v-model:activeKey="activeKey" class="detail-tabs">
          <a-tab-pane key="1" tab="订单">
            <div class="tab-content">
              <div class="table-container">
                <h-table
                  :columns="detailOrderColumns"
                  :data-source="getOrderData()"
                  :pagination="false"
                  :scroll="{ x: 'max-content', y: 'calc(100vh - 270px)' }"
                  bordered
                  size="small"
                />
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="发票">
            <div class="tab-content">
              <div class="invoice-total"><strong>发票金额合计：</strong>{{ calculateInvoiceTotal() }}</div>
              <div class="table-container">
                <h-table
                  :columns="detailInvoiceColumns"
                  :data-source="getInvoiceData()"
                  :pagination="false"
                  :scroll="{ x: 'max-content', y: 'calc(100vh - 270px)' }"
                  bordered
                  size="small"
                />
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>

        <!-- 上传操作区域（仅在上传模式下显示） -->
        <div v-if="isUploadMode && !isConsultant" class="upload-section">
          <div class="upload-content">
            <!-- 文件上传 -->
            <div class="upload-item">
              <div class="inline-label">付款凭证：</div>
              <div class="inline-content">
                <h-upload
                  v-model:fileList="fileList"
                  :custom-request="uploadRequest"
                  :maxCount="5"
                  :multiple="true"
                  :show-upload-list="true"
                  accept=".pdf, .doc, .docx, .jpg, .png, .jpeg, .xls, .xlsx"
                  @remove="handleFileRemove"
                >
                  <h-button :loading="uploadLoading">
                    <UploadOutlined />
                    上传文件
                  </h-button>
                </h-upload>
                <div class="upload-tip">支持格式：PDF、Word、Excel、图片，最多上传5个文件</div>
              </div>
            </div>

            <!-- 驳回原因 -->
            <div class="reject-item">
              <div class="inline-label">驳回原因：</div>
              <div class="inline-content">
                <a-textarea
                  v-model:value="ReasonsRejection"
                  :maxLength="200"
                  :rows="4"
                  allowClear
                  placeholder="请填写驳回原因（驳回时必填）"
                  show-count
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 权限提示（仅在上传模式下显示） -->
        <div v-if="isUploadMode && isConsultant" class="permission-tip">
          <h-card>
            <div class="tip-content">
              <p>您当前为会务顾问角色，只能查看付款单信息，无法上传付款凭证。</p>
            </div>
          </h-card>
        </div>

        <!-- 底部操作按钮 -->
        <div class="footer-actions">
          <!-- 查看模式：只显示返回按钮 -->
          <template v-if="isViewMode">
            <h-button class="action-button" @click="goBack"> 返回 </h-button>
          </template>

          <!-- 上传模式：显示全部按钮 -->
          <template v-else>
            <h-button class="action-button" @click="goBack"> 取消 </h-button>
            <template v-if="!isConsultant">
              <h-button :loading="uploadLoading" class="action-button" @click="handleReject"> 驳回 </h-button>
              <h-button :loading="uploadLoading" class="action-button" type="primary" @click="submitUpload">
                确定
              </h-button>
            </template>
          </template>
        </div>
      </h-card>
    </Spin>
  </div>
</template>

<style lang="less" scoped>
.container {
  background-color: #f5f5f5;
  
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  .back-button {
    margin-right: 16px;
    display: flex;
    align-items: center;
    padding: 4px 8px;
  }

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 500;
  }
}

.detail-card {
  background: white;
  min-height:calc(100vh - 113px);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .payment-info-content {
    padding: 12px 16px;
    width: 100%;
    height: 100%;
    background: url('@/assets/image/scheme/mice_bgc.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 6px;
  }

  .payment-main-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  .payment-main-icon {
    width: 28px;
    height: 28px;
    background-image: url('@/assets/image/scheme/mice_name.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-right: 12px;
  }

  .payment-main-name {
    font-family: PingFangSCSemibold, PingFangSCSemibold;
    font-weight: normal;
    font-size: 20px;
    color: #1d2129;
    line-height: 28px;
  }

  .payment-code {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;
  }

  .payment-info-details {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .payment-info-item {
    flex: 1;
    min-width: 0;
    font-size: 14px;
    color: #86909c;
    line-height: 15px;
  }

  .attachment-section {
    margin-top: 16px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #86909c;

    .attachment-files {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-left: 8px;
    }
  }

  .info-title {
    display: inline-block;
    text-indent: 26px;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center left;
    margin-right: 8px;

    &.amount-icon {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    &.file-icon {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    &.status-icon {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }

    &.remark-icon {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }
  }

  .info-value {
    color: #1d2129;
  }

  .file-link {
    margin-right: 16px;
    color: #1890ff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .detail-tabs {
    .tab-content {
      .table-container {
        overflow: hidden;
        border: 1px solid #f0f0f0;
        border-radius: 6px;

        :deep(.ant-table-wrapper) {
          height: 100%;

          .ant-table {
            height: 100%;

            .ant-table-container {
              height: 100%;

              .ant-table-tbody > tr > td {
                height: 1px;
              }

              .ant-table-thead > tr > th {
                padding: 8px 16px;
              }

              .ant-table-body {
                height: calc(100% - 55px);
                overflow-y: auto !important;
              }
            }
          }
        }
      }
    }

    .invoice-total {
      margin-bottom: 16px;
      padding: 12px 16px;
      background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%);
      border-radius: 6px;
      border-left: 4px solid #1890ff;

      strong {
        color: #1890ff;
        font-size: 14px;
      }
    }
  }

  .upload-section {
    margin-bottom: 24px;

    .upload-content {
      .upload-item,
      .reject-item {
        margin-top: 10px;
        display: flex;
        align-items: flex-start;
        gap: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .inline-label {
          flex-shrink: 0;
          width: 80px;
          font-weight: 500;
          color: #262626;
          padding-top: 6px;
        }

        .inline-content {
          flex: 1;
        }

        .upload-tip {
          margin-top: 8px;
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }

  .permission-tip {
    margin-bottom: 24px;

    .tip-content {
      text-align: center;
      color: #8c8c8c;
      padding: 24px;
    }
  }

  .footer-actions {
    position: fixed;
    width: calc(100% - 240px);
    bottom: 16px;
    right: 10px;
    text-align: right;
    padding: 8px 16px;
    border-top: 1px solid #f0f0f0;

    .action-button {
      margin-left: 12px;
    }
  }
}
:deep(.ant-spin-nested-loading){
  height: 100%;
  .ant-spin-container{
    height: 100%;
  }
}
</style>
