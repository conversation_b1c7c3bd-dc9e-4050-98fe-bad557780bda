<!-- 缴费单 -->
<script lang="ts" setup>
import {
  Button as hButton,
  Col as hCol,
  Input as hInput,
  message,
  Modal,
  RangePicker as hRangePicker,
  Row as hRow,
  Select as hSelect,
  SelectOption as hSelectOption,
  Table as hTable,
  Table as ATable,
  TableProps,
  Tag as hTag,
  Tooltip,
} from 'ant-design-vue'
import { ColumnType } from 'ant-design-vue/lib/table/interface'
import { Key } from 'ant-design-vue/lib/vc-table/interface'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { paymentFromApi } from '@haierbusiness-front/apis'
import { IPaymentFromFilter, PaymentFromStatusEnum, PaymentFromStatusMap } from '@haierbusiness-front/common-libs'
import dayjs, { Dayjs } from 'dayjs'
import { computed, onMounted, ref, watch } from 'vue'
import { DataType, usePagination } from 'vue-request'
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton'
import router from '../../router'

import Actions from '@haierbusiness-front/components/actions/Actions.vue'
import type { MenuInfo, MenuItemType } from 'ant-design-vue/lib/menu/src/interface'
// const router = useRouter()

const currentRouter = ref()
const store = applicationStore()
console.log(store.loginUser?.authorities, 'store')

// 权限判断：检查是否为会务顾问或会务负责人
const hasPaymentPermission = computed(() => {
  if (!store.loginUser?.authorities) {
    return false
  }

  return store.loginUser.authorities.some((item) => item.authority === '211' || item.authority === '213')
})



// 状态颜色映射
const getStatusColor = (status: number) => {
  switch (status) {
    case PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD:
      return 'orange'
    case PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM:
      return 'blue'
    case PaymentFromStatusEnum.PAYMENT_REJECTED:
      return 'red'
    case PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD:
      return 'cyan'
    case PaymentFromStatusEnum.APPROVAL_REJECTED:
      return 'volcano'
    case PaymentFromStatusEnum.APPROVING:
      return 'gold'
    case PaymentFromStatusEnum.COMPLETED:
      return 'green'
    default:
      return 'default'
  }
}

onMounted(async () => {
  currentRouter.value = await router
  // 页面初始化时调用列表接口
  listApiRun({
    pageNum: 1,
    pageSize: 10,
  })
})

const columns: ColumnType[] = [
  {
    title: '缴费单号',
    dataIndex: 'receivePaymentCode',
    width: '250px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '服务商名称',
    dataIndex: 'merchantName',
    width: '200px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '账单总金额',
    dataIndex: 'totalAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '结算比例',
    dataIndex: 'settlementRate',
    width: '80px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '收款金额',
    dataIndex: 'receivePaymentAmount',
    width: '200px',
    align: 'center',
    ellipsis: true,
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '150px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: '_operator',
    width: '100px',
    fixed: 'right',
    align: 'center',
  },
]
const searchParam = ref<IPaymentFromFilter>({})
const { data, run: listApiRun, loading } = usePagination(paymentFromApi.getPaymentPage)

// 生成缴费单弹框的独立查询参数
const paymentOrderSearchParam = ref<{
  startTime?: string
  endTime?: string
  merchantCode?: string
}>({})
const paymentOrderBeginAndEnd = ref<[Dayjs, Dayjs]>()

const reset = () => {
  searchParam.value = {}
  beginAndEnd.value = undefined
}

const dataSource = computed(() => data.value?.records || [])

const pagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: data.value?.total || 0,
  current: data.value?.pageNum || 1,
  pageSize: data.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}))

const handleTableChange = (pag: { current: number; pageSize: number }) => {
  listApiRun({
    ...searchParam.value,
    pageNum: pag.current,
    pageSize: pag.pageSize,
  })
}



// 审批流相关
const approveCode = ref<string>('')
const approvalModalShow = ref<boolean>(false)
const businessProcess = import.meta.env.VITE_BUSINESS_PROCESS_URL

// 查看
const handleView = (record?: any) => {
  if (record && record.id) {
    // 跳转到详情页面
    currentRouter.value.push({
      path: '/bidman/PaymentDocumentManager/PaymentBillDetailModal',
      query: {
        record: encodeURIComponent(JSON.stringify({ id: record.id })),
      },
    })
  }
}

const beginAndEnd = ref<[Dayjs, Dayjs]>()
watch(
  () => beginAndEnd.value,
  (n: any) => {
    if (n) {
      searchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
      searchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
    } else {
      searchParam.value.startTime = undefined
      searchParam.value.endTime = undefined
    }
  },
)

// 监听生成缴费单弹框的时间选择器变化
watch(
  () => paymentOrderBeginAndEnd.value,
  (n: any) => {
    if (n) {
      paymentOrderSearchParam.value.startTime = dayjs(n[0]).format('YYYY-MM-DD 00:00:00')
      paymentOrderSearchParam.value.endTime = dayjs(n[1]).format('YYYY-MM-DD 23:59:59')
    } else {
      paymentOrderSearchParam.value.startTime = undefined
      paymentOrderSearchParam.value.endTime = undefined
    }
  },
)

// 关闭生成缴费单弹窗
const closePaymentOrderModal = () => {
  PaymentOrderVisible.value = false
  settlementList.value = []
  selectedRowKeys.value = []
  // 重置弹窗中的查询条件
  paymentOrderBeginAndEnd.value = undefined
  paymentOrderSearchParam.value = {}
}


// 上传付款单
const PaymentOrderVisible = ref(false)
// 选择的结算单
const settlementList = ref<any[]>([])

const {
  data: PaymentOrderData,
  run: PaymentOrderlist,
  loading: paymentOrderLoading,
} = usePagination(paymentFromApi.getBalnceList)

const handlePaymentOrder = () => {
  PaymentOrderlist({
    startTime: paymentOrderSearchParam.value.startTime,
    endTime: paymentOrderSearchParam.value.endTime,
    merchantCode: paymentOrderSearchParam.value.merchantCode,
    pageNum: 1,
    pageSize: 10,
  })
}

// PaymentOrder表格分页
const paymentOrderPagination = computed(() => ({
  showSizeChanger: true,
  showQuickJumper: true,
  total: PaymentOrderData.value?.total || 0,
  current: PaymentOrderData.value?.pageNum || 1,
  pageSize: PaymentOrderData.value?.pageSize || 10,
  style: { justifyContent: 'center' },
}))

// 处理PaymentOrder表格分页变化
const handlePaymentOrderTableChange = (pag: any, filters?: any, sorter?: any) => {
  PaymentOrderlist({
    startTime: paymentOrderSearchParam.value.startTime,
    endTime: paymentOrderSearchParam.value.endTime,
    merchantCode: paymentOrderSearchParam.value.merchantCode,
    pageNum: pag.current || 1,
    pageSize: pag.pageSize || 10,
  })
}
const selectedRowKeys = ref<Key[]>([])

// 订单
const PaymentOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '会议时间',
    dataIndex: 'meetingTime',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`
      }
      return ''
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
    ellipsis: true,
  },
  {
    title: '结算金额',
    dataIndex: 'amount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '收款金额',
    dataIndex: 'receiveAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
]

// 提交生成付款单
const submitPaymentOrder = () => {
  if (!settlementList.value || settlementList.value.length === 0) {
    message.error('请选择会议')
    return
  }

  // 提取选中会议的balanceNo列表
  const balanceIds = settlementList.value.map((item: any) => item.id).filter(Boolean)

  if (balanceIds.length === 0) {
    message.error('选中的会议中没有有效的结算单号')
    return
  }

  // 提取merchantCode并检查是否为同一服务商
  const merchantCodes = settlementList.value.map((item: any) => item.merchantCode).filter(Boolean)
  const uniqueMerchantCodes = [...new Set(merchantCodes)]

  if (uniqueMerchantCodes.length === 0) {
    message.error('选中的会议中没有有效的服务商信息')
    return
  }

  if (uniqueMerchantCodes.length > 1) {
    message.error('只能选择同一个服务商的会议进行生成缴费单')
    return
  }

  // 调用生成付款单接口
  paymentFromApi
    .createPayment({
      balanceIds: balanceIds,
      merchantCode: uniqueMerchantCodes[0] as string,
    })
    .then(() => {
      message.success('缴费单生成成功')
      closePaymentOrderModal()
      // 刷新列表
      listApiRun({
        ...searchParam.value,
        pageNum: data.value?.pageNum || 1,
        pageSize: data.value?.pageSize || 10,
      })
    })
    .catch((error) => {
      console.error('生成缴费单失败:', error)
      message.error('生成缴费单失败，请重试')
    })
}

// 跳转到上传发票页面
const navigateToUploadInvoice = (record: any) => {
  currentRouter.value.push({
    path: '/bidman/PaymentDocumentManager/uploadInvoice',
    query: {
      record: encodeURIComponent(JSON.stringify(record)),
      type: 'manage', // 明确指定为缴费场景
    },
  })
}

// 处理菜单点击事件
const handleMenuClick = (record: any, e: MenuInfo) => {
  const key = e.key as string
  switch (key) {
    case 'view':
      // 查看详情
      handleView(record)
      break
    case 'uploadInvoice':
      navigateToUploadInvoice(record)
      break
    case 'resubmit':
      // 重新提交审批操作
      resubmitApproval(record)
      break
    default:
      break
  }
}

// 状态操作配置映射
const STATUS_ACTION_CONFIG: Record<number, { key: string; label: string }[]> = {
  [PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD]: [
    { key: 'uploadInvoice', label: '上传发票' }
  ],
  [PaymentFromStatusEnum.APPROVAL_REJECTED]: [
    { key: 'resubmit', label: '重新上传发票' }
  ],
}

// 计算菜单选项
const getMenuOptions = (record: any) => {
  const options: MenuItemType[] = []

  // 查看按钮始终显示
  options.push({
    key: 'view',
    label: '查看',
  })

  // 根据状态添加对应的操作选项
  const statusActions = STATUS_ACTION_CONFIG[record.status] || []
  options.push(...statusActions)

  return options
}

// 重新提交审批操作 - 重新走上传发票流程
const resubmitApproval = (record: any) => {
  if (!record?.id) {
    message.error('记录ID不存在')
    return
  }

  // 直接跳转到上传发票页面，重新走完整流程
  navigateToUploadInvoice(record)
}

// 关闭审批弹窗
const closeApproval = () => {
  approvalModalShow.value = false
  approveCode.value = ''
}

// 生成付款单
const generatePaymentOrder = () => {
  // 先清空之前的选择状态
  settlementList.value = []
  selectedRowKeys.value = []
  PaymentOrderVisible.value = true
  // 打开弹窗时就调用接口获取数据
  PaymentOrderlist({
    startTime: paymentOrderSearchParam.value.startTime,
    endTime: paymentOrderSearchParam.value.endTime,
    merchantCode: paymentOrderSearchParam.value.merchantCode,
    pageNum: 1,
    pageSize: 10,
  })
}
</script>

<template>
  <div class="page-container">
    <h-row :align="'middle'">
      <h-col :span="24" style="margin-bottom: 10px">
        <h-row :align="'middle'" class="search-row">
          <h-col :span="2" class="search-label">
            <label for="serviceProvider">服务商：</label>
          </h-col>
          <h-col :span="4">
            <h-input v-model:value="searchParam.merchantCode" allow-clear placeholder="请输入服务商" />
          </h-col>
          <h-col :span="3" class="search-label">
            <label for="createTime">缴费单创建时间：</label>
          </h-col>
          <h-col :span="4">
            <h-range-picker v-model:value="beginAndEnd" allow-clear style="width: 100%" value-format="YYYY-MM-DD" />
          </h-col>

          <h-col :span="2" class="search-label">
            <label for="status">状态：</label>
          </h-col>
          <h-col :span="4">
            <h-select v-model:value="searchParam.status" allow-clear placeholder="请选择状态" style="width: 100%">
              <h-select-option :value="PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_PAYMENT_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_FINANCIAL_CONFIRM] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.PAYMENT_REJECTED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PAYMENT_REJECTED] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.PENDING_INVOICE_UPLOAD] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.APPROVAL_REJECTED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.APPROVAL_REJECTED] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.APPROVING">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.APPROVING] }}
              </h-select-option>
              <h-select-option :value="PaymentFromStatusEnum.COMPLETED">
                {{ PaymentFromStatusMap[PaymentFromStatusEnum.COMPLETED] }}
              </h-select-option>
            </h-select>
          </h-col>
        </h-row>
        <h-row :align="'middle'" class="search-row">
          <h-col :span="24" class="button-row">
            <h-button class="button-margin" @click="reset">重置</h-button>
            <h-button type="primary" @click="handleTableChange({ current: 1, pageSize: 10 })">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <h-row :align="'middle'" class="search-row">
          <h-col :span="12" class="button-row-left">
            <h-button v-if="hasPaymentPermission" type="primary" @click="generatePaymentOrder">
              <PlusOutlined />
              生成缴费单
            </h-button>
          </h-col>
        </h-row>
      </h-col>
      <h-col :span="24">
        <h-table
          :columns="columns"
          :data-source="dataSource"
          :loading="loading"
          :pagination="pagination"
          :row-key="(record) => record.id"
          :scroll="{ x: 1400 }"
          :size="'small'"
          @change="handleTableChange($event as any)"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'receivePaymentCode'">
              <Tooltip :title="record.receivePaymentCode">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.receivePaymentCode }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'merchantName'">
              <Tooltip :title="record.merchantName">
                <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">
                  {{ record.merchantName }}
                </div>
              </Tooltip>
            </template>
            <template v-if="column.dataIndex === 'status'">
              <h-tag :color="getStatusColor(record.status)">
                {{ PaymentFromStatusMap[record.status as keyof typeof PaymentFromStatusMap] || '未知状态' }}
              </h-tag>
            </template>
            <template v-if="column.dataIndex === '_operator'">
              <Actions
                :menu-options="getMenuOptions(record)"
                :on-menu-click="(e: MenuInfo) => handleMenuClick(record, e)"
              />
            </template>
          </template>
        </h-table>
      </h-col>
    </h-row>
    <!-- 生成付款单弹窗 -->
    <Modal
      v-model:open="PaymentOrderVisible"
      :footer="null"
      title="生成缴费单"
      width="60%"
      @cancel="closePaymentOrderModal"
    >
      <div>
        <h-row :align="'middle'" class="modal-search-row">
          <h-col :span="3" class="search-label">
            <label for="merchantCode">服务商名称：</label>
          </h-col>
          <h-col :span="5">
            <h-input v-model:value="paymentOrderSearchParam.merchantCode" allow-clear placeholder="支持名字和V码搜索" />
          </h-col>

          <h-col :span="4" class="search-label">
            <label for="createTime">会议时间：</label>
          </h-col>
          <h-col :span="6">
            <h-range-picker
              v-model:value="paymentOrderBeginAndEnd"
              allow-clear
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </h-col>

          <h-col :span="6" class="modal-search-button">
            <h-button type="primary" @click="handlePaymentOrder()">
              <SearchOutlined />
              查询
            </h-button>
          </h-col>
        </h-row>
        <div class="modal-table-container">
          <a-table
            :columns="PaymentOrderColumns"
            :data-source="PaymentOrderData?.records || []"
            :loading="paymentOrderLoading"
            :pagination="paymentOrderPagination"
            :row-key="(record) => record.id"
            :row-selection="{
              selectedRowKeys: selectedRowKeys,
              onChange: (keys: Key[], rows: DataType[]) => {
                selectedRowKeys = keys
                settlementList = rows
              },
            }"
            :scroll="{ x: 'max-content' }"
            class="modal-table"
            @change="handlePaymentOrderTableChange"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'mainCode'">
                <Tooltip :title="text">
                  <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">{{ text }}</div>
                </Tooltip>
              </template>
            </template>
          </a-table>
        </div>
        <div class="modal-footer">
          <h-button class="button-margin" @click="closePaymentOrderModal">取消</h-button>
          <h-button :loading="paymentOrderLoading" type="primary" @click="submitPaymentOrder">生成缴费单</h-button>
        </div>
      </div>
    </Modal>



    <!-- 审批流弹窗 -->
    <Modal
      v-model:open="approvalModalShow"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      title="审批流程"
      width="80%"
    >
      <div>
        <iframe :src="businessProcess + '?code=' + approveCode + '#/detailsPcSt'" frameborder="0" width="100%"></iframe>
      </div>
      <template #footer>
        <h-button @click="closeApproval">确定</h-button>
      </template>
    </Modal>
  </div>
</template>

<style lang="less" scoped>
// 页面容器
.page-container {
  background-color: #ffff;
  height: 100%;
  width: 100%;
  padding: 10px 10px 0px 10px;
  overflow: auto;
}

// 查询条件行
.search-row {
  padding: 10px 10px 0px 10px;
}

// 标签样式
.search-label {
  text-align: right;
  padding-right: 10px;
}

// 按钮行
.button-row {
  text-align: right;
}

.button-row-left {
  text-align: left;
}

// 按钮间距
.button-margin {
  margin-right: 10px;
}

// 弹框查询条件
.modal-search-row {
  padding: 10px 10px 0px 10px;
}

.modal-search-button {
  text-align: left;
  padding-left: 10px;
}

// 表格样式
.modal-table {
  margin-top: 15px;
}

.modal-footer {
  text-align: right;
  margin-top: 20px;
}

// 原有样式
.img-con {
  width: 100%;
  display: flex;
  justify-content: center;
}

.img {
  width: 104.5px;
  height: 50px;
}



:deep(table) {
  table-layout: auto !important; /* 恢复默认自适应布局 */
}
</style>
