<!-- 缴费单详情弹框组件 -->
<script lang="ts" setup>
import { message, Table as hTable, TabPane as ATabPane, Tabs as ATabs, Spin, Card as hCard, But<PERSON> as hButton } from 'ant-design-vue'
import { ColumnType } from 'ant-design-vue/lib/table/interface'
import { inject, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { DataType } from 'vue-request'
import { resolveParam, getFileNameFromPath } from '@haierbusiness-front/utils'
import { paymentFromApi } from '@haierbusiness-front/apis'
import { PaymentFromStatusEnum, PaymentFromStatusMap, FileTypeEnum } from '@haierbusiness-front/common-libs'



// 路由检测
const route = useRoute()
const routeRecord = route.query.record ? resolveParam(route.query.record as string) : null
const hideBtn = routeRecord?.hideBtn || ''
const frameModel = inject<any>('frameModel')
if (frameModel) {
  frameModel.value = hideBtn === '1' ? 1 : 0 // frameModel-1,隐藏;0-显示
}
// 响应式数据
const activeKey = ref('1')
const detailData = ref<any>(null)
const loading = ref(false)

// 获取详情数据
const fetchDetailData = async () => {
  if (!routeRecord?.id) {
    return
  }

  loading.value = true
  try {
    const res = await paymentFromApi.getPaymentDetails(routeRecord.id)
    detailData.value = res
  } catch (error) {
    console.error('获取详情失败:', error)
    message.error('获取详情失败，请重试')
  } finally {
    loading.value = false
  }
}



// 组件挂载时获取数据
onMounted(() => {
  fetchDetailData()
})

// 详情页订单表格列
const detailOrderColumns: ColumnType<DataType>[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '150px',
    align: 'center',
  },
  {
    title: '会议时间',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`
      }
      return ''
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'receivePaymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
]


// 详情页发票表格列
const detailInvoiceColumns: ColumnType<DataType>[] = [
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    align: 'center',
  },
  {
    title: '发票日期',
    dataIndex: 'invoiceDate',
    align: 'center',
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
]

// 计算发票金额合计
const calculateInvoiceTotal = () => {
  if (!detailData.value) {
    return '0'
  }
  const invoiceData = getInvoiceData()
  const total = invoiceData.reduce((sum: number, item: any) => sum + (item.invoiceAmount || 0), 0)
  return `${total}元`
}

// 获取发票数据
const getInvoiceData = () => {
  if (!detailData.value || !detailData.value.miceInvoiceQueryDetails) {
    return []
  }
  // 使用miceInvoiceQueryDetails作为发票数据源
  return detailData.value.miceInvoiceQueryDetails.map((invoice: any, index: number) => ({
    key: invoice.id || index,
    invoiceNumber: invoice.invoiceNumber || '',
    invoiceDate: invoice.invoiceDate || '',
    invoiceAmount: invoice.invoiceAmount || 0,
    isConfirmed: invoice.isConfirmed || 0,
  }))
}

// 判断是否应该显示驳回原因
const shouldShowRejectReason = (data: any) => {
  if (!data) {
    return false
  }
  const status = data.paymentStatus || data.status
  // 收款驳回(30)时检查remark字段
  if (status === PaymentFromStatusEnum.PAYMENT_REJECTED && data.remark) {
    return true
  }
  // 审批驳回(50)时检查invoiceRemark字段
  if (status === PaymentFromStatusEnum.APPROVAL_REJECTED && data.invoiceRemark) {
    return true
  }
  return false
}

// 获取驳回原因文本
const getRejectReasonText = (data: any) => {
  const status = data.paymentStatus || data.status
  // 审批驳回时使用invoiceRemark，其他状态使用remark
  if (status === PaymentFromStatusEnum.APPROVAL_REJECTED) {
    return data.invoiceRemark || ''
  }
  return data.remark || ''
}

// 获取状态文本
const getStatusText = (data: any) => {
  const status = data.paymentStatus || data.status
  return PaymentFromStatusMap[status as keyof typeof PaymentFromStatusMap] || '未知状态'
}
</script>

<template>
  <div class="container">
    <Spin :spinning="loading">
      <h-card v-if="detailData" class="detail-card">
        <!-- 基本信息 -->
        <div class="payment-info-card">
          <div class="payment-info-content">
            <!-- 主标题：服务商名称 -->
            <div class="payment-main-title">
              <div class="payment-main-icon"></div>
              <span class="payment-main-name">{{ detailData.merchantName || '未知服务商' }}</span>
            </div>

            <!-- 缴费单号 -->
            <div class="payment-code">
              <span>缴费单号：{{ detailData.receivePaymentCode || '-' }}</span>
            </div>

            <!-- 详细信息 -->
            <div class="payment-info-details">
              <div class="payment-info-item">
                <span class="info-title amount-icon">付款总金额：</span>
                <span class="info-value">{{
                  detailData.receivePaymentAmount ? `${detailData.receivePaymentAmount}元` : '-'
                }}</span>
              </div>
              <div class="payment-info-item">
                <span class="info-title status-icon">创建时间：</span>
                <span class="info-value">{{ detailData.gmtCreate || '-' }}</span>
              </div>
              <div class="payment-info-item">
                <span class="info-title status-icon">状态：</span>
                <span class="info-value">{{ getStatusText(detailData) }}</span>
              </div>
              <div v-if="shouldShowRejectReason(detailData)" class="payment-info-item">
                <span class="info-title remark-icon">驳回原因：</span>
                <span class="info-value">{{ getRejectReasonText(detailData) }}</span>
              </div>
            </div>

            <div class="attachment-section">
              <div class="attachment-row">
                <!-- 付款凭证 -->
                <div v-if="detailData.attachmentFiles && detailData.attachmentFiles.some((f: any) => f.type === FileTypeEnum.PAYMENT_VOUCHER_SERVICE_PROVIDER)" class="attachment-item">
                  <span class="info-title file-icon">付款凭证：</span>
                  <div class="attachment-files">
                    <template v-for="(file, index) in detailData.attachmentFiles.filter((f: any) => f.type === FileTypeEnum.PAYMENT_VOUCHER_SERVICE_PROVIDER)" :key="'payment-'+index">
                      <a :href="file.path" class="file-link" target="_blank">
                        {{ getFileNameFromPath(file.path || `付款凭证${index + 1}`) }}
                      </a>
                    </template>
                  </div>
                </div>

                <!-- 收款凭证 -->
                <div v-if="detailData.attachmentFiles && detailData.attachmentFiles.some((f: any) => f.type === FileTypeEnum.RECEIPT_VOUCHER_FINANCE)" class="attachment-item">
                  <span class="info-title file-icon">收款凭证：</span>
                  <div class="attachment-files">
                    <template v-for="(file, index) in detailData.attachmentFiles.filter((f: any) => f.type === FileTypeEnum.RECEIPT_VOUCHER_FINANCE)" :key="'receipt-'+index">
                      <a :href="file.path" class="file-link" target="_blank">
                        {{ getFileNameFromPath(file.path || `收款凭证${index + 1}`) }}
                      </a>
                    </template>
                  </div>
                </div>

                <!-- 收款单号 -->
                <div class="attachment-item">
                  <span class="info-title">收款单号：</span>
                  <span class="info-value">{{ detailData.sapReceiveCode || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Tab页 -->
        <a-tabs v-model:active-key="activeKey" class="detail-tabs">
          <a-tab-pane key="1" tab="订单">
            <div class="tab-content">
              <div class="table-container">
                <h-table
                  :columns="detailOrderColumns"
                  :data-source="detailData.receivePaymentRecordsDetails || []"
                  :pagination="false"
                  :scroll="{ x: 'max-content', y: 400 }"
                  bordered
                  size="small"
                />
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="2" tab="发票">
            <div class="tab-content">
              <div class="invoice-total"><strong>发票金额合计：</strong>{{ calculateInvoiceTotal() }}</div>
              <div class="table-container">
                <h-table
                  :columns="detailInvoiceColumns"
                  :data-source="getInvoiceData()"
                  :pagination="false"
                  :scroll="{ x: 'max-content', y: 400 }"
                  bordered
                  size="small"
                />
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>

        <!-- 底部操作按钮 -->
        <div v-if="hideBtn !== '1'" class="footer-actions">
          <h-button class="action-button" @click="$router.go(-1)"> 返回 </h-button>
        </div>
      </h-card>
    </Spin>
  </div>
</template>

<style lang="less" scoped>
// 页面模式样式
.container {
  background-color: #f5f5f5;
  height: 100%;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.detail-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .payment-info-content {
    padding: 10px 0px 10px 0px;
    width: 100%;
    height: 100%;
    background: url('@/assets/image/scheme/mice_bgc.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 6px;
  }

  .payment-main-title {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  .payment-main-icon {
    width: 28px;
    height: 28px;
    background-image: url('@/assets/image/scheme/mice_name.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-right: 12px;
  }

  .payment-main-name {
    font-family: PingFangSCSemibold, PingFangSCSemibold;
    font-weight: normal;
    font-size: 20px;
    color: #1d2129;
    line-height: 28px;
  }

  .payment-code {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;
  }

  .payment-info-details {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .payment-info-item {
    width: 24%;
    min-width: 0;
    font-size: 14px;
    color: #86909c;
    line-height: 15px;
  }

  .attachment-section {
    margin-top: 16px;
    font-size: 14px;
    color: #86909c;

    .attachment-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }

    .attachment-item {
      display: flex;
      align-items: flex-start;
      width: 24%;
      min-width: 0;
      font-size: 14px;
      color: #86909c;
      line-height: 15px;
    }

    .attachment-files {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-left: 8px;
    }
  }

  .info-title {
    display: inline-block;
    text-indent: 26px;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center left;
    margin-right: 8px;

    &.amount-icon {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    &.file-icon {
      background-image: url('@/assets/image/scheme/mice_type.png');
    }

    &.status-icon {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }

    &.remark-icon {
      background-image: url('@/assets/image/scheme/mice_person.png');
    }
  }

  .info-value {
    color: #1d2129;
  }

  .file-link {
    margin-right: 16px;
    color: #1890ff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .detail-tabs {
    .tab-content {
      min-height: 200px;

      .table-container {
        height: 450px;
        overflow: hidden;
        border: 1px solid #f0f0f0;
        border-radius: 6px;

        :deep(.ant-table-wrapper) {
          height: 100%;

          .ant-table {
            height: 100%;

            .ant-table-container {
              height: 100%;

              .ant-table-thead > tr > th {
                padding: 8px 6px;
                background-color: #fafafa;
                border-bottom: 1px solid #f0f0f0;
                text-align: center;
                white-space: nowrap;
              }

              .ant-table-tbody > tr > td {
                padding: 8px 6px;
                height: auto;
                text-align: center;
                white-space: nowrap;
              }

              .ant-table-body {
                height: calc(100% - 55px);
                overflow-y: auto !important;
                overflow-x: hidden;
              }

              // 解决滚动条导致的对齐问题
              .ant-table-header {
                overflow: hidden;
                margin-bottom: 0 !important;
                
                .ant-table-thead > tr > th {
                  border-right: 1px solid #f0f0f0;
                  
                  &:last-child {
                    border-right: none;
                  }
                }
              }

              .ant-table-tbody > tr > td {
                border-right: 1px solid #f0f0f0;
                
                &:last-child {
                  border-right: none;
                }
              }
            }
          }
        }
      }
    }

    .invoice-total {
      margin-bottom: 16px;
      padding: 12px 16px;
      background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 100%);
      border-radius: 6px;
      border-left: 4px solid #1890ff;

      strong {
        color: #1890ff;
        font-size: 14px;
      }
    }
  }

  .footer-actions {
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;

    .action-button {
      margin-left: 12px;
    }
  }
}



// 表格布局
:deep(.detail-tabs .ant-table table) {
  table-layout: fixed !important;
  width: 100%;
}

// 确保表格整体对齐
:deep(.detail-tabs .ant-table) {
  .ant-table-header table,
  .ant-table-body table {
    table-layout: fixed !important;
    width: 100% !important;
  }
}
</style>
