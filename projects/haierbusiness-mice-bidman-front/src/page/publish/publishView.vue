// 需求发布预览页面 - 平台端
<script lang="ts" setup>
import { message } from 'ant-design-vue'
import Advisors from '@haierbusiness-front/components/mice/advisors/index.vue'
import { inject, onMounted, ref } from 'vue'
import MeetingConsultantDrawer from '@haierbusiness-front/components/meetingConsultantDrawer/index.vue'
import { resolveParam, routerParam } from '@haierbusiness-front/utils'

import { miceBidManOrderListApi } from '@haierbusiness-front/apis'
import { SearchData } from '@haierbusiness-front/common-libs'
import { useRoute, useRouter } from 'vue-router'

// 状态变量定义
const route = useRoute()
const router = useRouter()
const isCloseLastTab = inject<ref<boolean>>('isCloseLastTab')

const consultantModalOpen = ref(false) // 编辑抽屉是否打开
const searchData = ref<SearchData[]>([]) // 顾问列表数据
const searchDataSum = ref<SearchData[]>([]) // 顾问列表数据备份
const rejectModalOpen = ref(false) // 驳回弹窗是否打开
const demandRejectReason = ref('') // 驳回原因

// 分配人员
const assignPerson = async (item) => {
  const res = await miceBidManOrderListApi.userAssign({
    username: item.username,
    id: route.query.miceId,
  })

  if (res.success) {
    message.success('分配成功')
    consultantModalOpen.value = false
  }
}

// 处理发布
const frameModel = ref(inject<any>('frameModel')) // 是否隐藏侧边栏
const isHideBtn = ref(false) // 是否隐藏底部按钮

// 需求发布
const handlePublish = () => {
  router.push({
    path: '/bidman/publish/index',
    query: {
      record: routerParam({ miceId: route.query.miceId, miceDemandId: route.query.miceDemandId, status: '1' }),
    },
  })

  // 关闭当前标签页
  isCloseLastTab.value = true
}

// 驳回需求
const rejectPush = async () => {
  if (demandRejectReason.value) {
    const res = await miceBidManOrderListApi.cancelPush({
      miceId: route.query.miceId,
      demandRejectReason: demandRejectReason.value,
    })

    if (res.success) {
      message.success('驳回成功')
      rejectModalOpen.value = false

      // 跳转订单列表
      router.push({ path: '/bidman/orderList/index', query: { status: '0' } })

      // 关闭当前标签页
      isCloseLastTab.value = true
    }
  } else {
    message.error('驳回原因不能为空！')
  }
}

// 搜索关键词
const searchKeyword = (value) => {
  if (value)
    searchData.value = searchDataSum.value.filter((item) => {
      if (item.username.includes(value) || item.name.includes(value)) {
        return item
      }
    })
  else searchData.value = searchDataSum.value
}

// 发布视图
const publishJump = () => {
  router.push({
    path: '/bidman/publish/index',
    query: route.query,
  })
}

onMounted(() => {
  isHideBtn.value = route.query.hideBtn == '1'
  frameModel.value = isHideBtn.value ? 1 : 0
})
</script>
<template>
  <!-- 顾问列表页面主容器 -->
  <div class="container">
    <!-- 顾问列表组件 -->
    <Advisors :class="isHideBtn ? 'footer-user-width' : ''" preview-source="demandOne">
      <!-- 头部插槽：备忘录和日志按钮 -->
      <template #header></template>
      <!-- 底部插槽：操作按钮组 -->
      <template #footer>
        <div v-if="isHideBtn" class="footer">
          <a-radio-group button-style="solid" value="demand">
            <a-radio-button value="demand">需求视图</a-radio-button>
            <a-radio-button value="push" @click="publishJump">发布视图</a-radio-button>
          </a-radio-group>
        </div>
        <div v-else>
          <a-button danger size="small" style="margin-right: 10px" type="primary" @click="rejectModalOpen = true">需求驳回</a-button>
          <a-button size="small" style="margin-right: 10px" type="primary" @click="handlePublish">需求发布</a-button>
        </div>
      </template>
    </Advisors>

    <!-- 驳回原因弹窗 -->
    <a-modal v-model:open="rejectModalOpen" title="驳回原因" @ok="rejectPush">
      <a-textarea v-model:value="demandRejectReason" :maxLength="200" :rows="4" placeholder="请输入驳回原因" />
    </a-modal>

    <!-- 会务顾问抽屉组件 -->
    <meeting-consultant-drawer
      v-model="consultantModalOpen"
      :items="searchData"
      title="会务顾问"
      @assign="assignPerson"
      @search="searchKeyword"
    />
  </div>
</template>
<style lang="less" scoped>
.container {
  background: #f1f2f6;
  padding: 0 auto;
}

.footer-user-width {
  width: 1280px !important;
  left: calc(50% - 640px);
}

:deep(.ant-btn-default) {
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  text-align: center;
  font-style: normal;
}

:deep(.ant-btn-primary) {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
}

.reject-btn {
  background: #f5222d;
}

.footer {
  text-align: left;
}
</style>
