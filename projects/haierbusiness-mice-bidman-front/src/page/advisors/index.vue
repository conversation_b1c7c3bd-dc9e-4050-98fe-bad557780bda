<script lang="ts" setup>
// 顾问列表页面
import { message } from 'ant-design-vue'
import Advisors from '@haierbusiness-front/components/mice/advisors/index.vue'
import { inject, ref } from 'vue'

import { miceBidManOrderListApi } from '@haierbusiness-front/apis'
import { SearchData } from '@haierbusiness-front/common-libs'
import { useRoute, useRouter } from 'vue-router'

import MeetingConsultantDrawer from '@haierbusiness-front/components/meetingConsultantDrawer/index.vue'

const route = useRoute()
const router = useRouter()
const isCloseLastTab = inject<ref<boolean>>('isCloseLastTab')

// 状态变量定义
const consultantModalOpen = ref(false) // 编辑抽屉是否打开
const searchData = ref<SearchData[]>([]) // 顾问列表数据
const searchDataSum = ref<SearchData[]>([]) // 顾问列表数据备份
const rejectModalOpen = ref(false) // 驳回弹窗是否打开
const demandRejectReason = ref('') // 驳回原因
const advisorsRef = ref<any>(null)

// 处理驳回操作
const handleReject = () => {
  rejectModalOpen.value = true
}

// 确认驳回
const handleOk = async () => {
  if (demandRejectReason.value !== '') {
    const res = await miceBidManOrderListApi.receive_reject({
      miceId: route.query.miceId,
      demandRejectReason: demandRejectReason.value,
    })

    if (res.success) {
      message.success('驳回成功')
      rejectModalOpen.value = false

      // 关闭抽屉
      consultantModalOpen.value = false

      // 关闭当前标签页
      isCloseLastTab.value = true

      // 跳转订单列表
      router.push({ path: '/bidman/orderList/index', query: { status: '0' } })
    }
  } else {
    message.error('驳回原因不能为空！')
  }
}

// 处理接收订单
const handleReceive = async () => {
  const curDate = new Date()

  // 获取顾问统计数据
  const res = await miceBidManOrderListApi.counsellorUserCount({
    'mainCountRequest.startDateStart': curDate.getFullYear() + '-01-01 00:00:00',
    'mainCountRequest.startDateEnd': curDate.getFullYear() + '-12-31 23:59:59',
    // processNodes: 'DEMAND_PRE_INTERACT,DEMAND_PUSH,SCHEME_APPROVAL,BID_PUSH,BILL_APPROVAL',
    processId: advisorsRef.value.processId || '',
  })

  if (res.length > 0) {
    searchData.value = []
    searchDataSum.value = []

    res.forEach((item) => {
      // 初始化统计数据数组
      let arr = [
        { label: '需求互动', value: 0, code: 'DEMAND_PRE_INTERACT' },
        { label: '需求发布', value: 0, code: 'DEMAND_PUSH' },
        { label: '方案审核', value: 0, code: 'SCHEME_APPROVAL' },
        { label: '竞价推送', value: 0, code: 'BID_PUSH' },
        { label: '账单审核', value: 0, code: 'BILL_APPROVAL' },
      ]

      // 更新统计数据
      item.results.forEach((subItem) => {
        arr.forEach((item1) => {
          if (item1.code === subItem.processNode) {
            item1.value = subItem.counts
          }
        })
      })

      // 构建顾问数据对象
      searchData.value.push({
        name: item.nickName,
        desc: item.description,
        username: item.username,
        isPerson: advisorsRef.value.intentionConsultantUserCode === item.username || '',
        link: item.username,
        phone: item.phone,
        isRecommend: item.recommend,
        path: item.path,
        performance: [
          {
            label: '2025年承担总量',
            value: item.totalCount + '单 ',
            // + item.totalPrice + '元'
          },
          {
            label: '正在进行中',
            value: item.doingCount + '单 ',
            //  + item.doingPrice + '元'
          },
        ],
        statistics: arr,
      })
    })

    // 按推荐状态排序
    searchData.value = searchData.value.sort((a, b) =>
      b.isRecommend ? b.isRecommend - a.isRecommend : b.isPerson - a.isPerson,
    )

    searchDataSum.value = searchData.value
  }

  consultantModalOpen.value = true
}

// 分配人员
const assignPerson = async (item) => {
  const res = await miceBidManOrderListApi.userAssign({
    miceId: route.query.miceId,
    consultantUserCode: item.username,
    consultantUserName: item.name,
    consultantUserPhone: item.phone,
  })

  if (res.success) {
    message.success('分配成功')
    consultantModalOpen.value = false

    // 关闭当前标签页
    isCloseLastTab.value = true

    // 跳转订单列表
    router.push({ path: '/bidman/orderList/index', query: { status: '0' } })
  }
}

// 处理发布
const handlePublish = () => {
  // 跳转需求发布页面
  router.push({
    path: '/bidman/publish/index',
    query: {
      record: JSON.stringify({ miceId: route.query.miceId, miceDemandId: route.query.miceDemandId, status: '1' }),
    },
  })
}

// 搜索关键词
const searchKeyword = (value) => {
  if (value) {
    searchData.value = searchDataSum.value.filter((item) => {
      if (item.username.includes(value) || item.name.includes(value)) {
        return item
      }
    })
  } else {
    // 重置顾问列表
    searchData.value = searchDataSum.value
  }
}
</script>
<template>
  <!-- 顾问列表页面主容器 -->
  <div>
    <!-- 顾问列表组件 -->
    <Advisors ref="advisorsRef" preview-source="demandOne">
      <!-- 头部插槽：备忘录和日志按钮 -->
      <template #header></template>
      <!-- 底部插槽：操作按钮组 -->
      <template v-if="!consultantModalOpen" #footer>
        <a-button v-show="false" size="small" style="margin-right: 10px" type="primary" @click="handlePublish"
          >需求驳回
        </a-button>
        <a-button v-show="false" size="small" style="margin-right: 10px" type="primary" @click="handlePublish"
          >需求发布
        </a-button>
        <a-button
          v-show="route.query.status === '1'"
          size="small"
          style="margin-right: 10px"
          type="primary"
          @click="handleReceive"
          >订单接收
        </a-button>
        <a-button
          v-show="route.query.status === '1'"
          class="reject-btn"
          danger
          size="small"
          style="margin-right: 10px"
          type="primary"
          @click="handleReject"
          >订单驳回
        </a-button>
      </template>
    </Advisors>

    <!-- 驳回原因弹窗 -->
    <a-modal v-model:open="rejectModalOpen" title="驳回" @ok="handleOk">
      <p>驳回原因</p>
      <a-textarea v-model:value="demandRejectReason" :maxLength="200" :rows="4" placeholder="请输入驳回原因" />
    </a-modal>

    <!-- 会务顾问抽屉组件 -->
    <meeting-consultant-drawer
      v-model="consultantModalOpen"
      :items="searchData"
      class="meeting-consultant-drawer"
      title="会务顾问"
      @assign="assignPerson"
      @search="searchKeyword"
    />
  </div>
</template>
<style lang="less" scoped>
:deep(.ant-btn-default) {
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  border-radius: 2px;
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #4e5969;
  line-height: 22px;
  text-align: center;
  font-style: normal;
}

:deep(.ant-btn-primary) {
  font-family:
    PingFangSC,
    PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  font-style: normal;
  padding: 3px 8px;
  height: 32px;
  width: 80px;
  background: #1868db;
  border-radius: 2px;
}

.reject-btn {
  background: #f5222d;
}
</style>
