<script lang="ts" setup>
// 方案详情 - 平台端
import Scheme from '@haierbusiness-front/components/mice/scheme/index.vue'
import SchemeView from '@haierbusiness-front/components/mice/scheme/view.vue'
import { useRoute } from 'vue-router'

const route = useRoute()
</script>

<template>
  <div>
    <SchemeView
      v-if="['/bidman/scheme/view', '/bidman/bid/view', '/bidman/scheme/confirm/view'].includes(route.path)"
      orderSource="manage"
    >
    </SchemeView>
    <Scheme
      v-if="['/bidman/scheme/index', '/bidman/bid/index', '/bidman/scheme/confirm'].includes(route.path)"
      orderSource="manage"
    >
    </Scheme>
  </div>
</template>
